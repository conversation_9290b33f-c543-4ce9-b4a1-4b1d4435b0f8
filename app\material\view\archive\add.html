{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.p-4 {
    padding: 15px;
}
.p-page {
    padding: 15px;
}
.layui-td-gray {
    background-color: #f8f8f8;
    font-weight: bold;
}
.upload-item {
    position: relative;
    display: inline-block;
    margin: 5px;
}
.upload-item img {
    border: 1px solid #ddd;
    border-radius: 4px;
}
</style>
{/block}

{block name="body"}
<div class="layui-tab layui-tab-brief" lay-filter="materialTab">
    <ul class="layui-tab-title">
        <li class="layui-this">基础资料</li>
        <li>质检信息</li>
        <li>价格信息</li>

        <li>工艺管理</li>
    </ul>
    <div class="layui-tab-content">
        <!-- 基础资料 -->
        <div class="layui-tab-item layui-show">
            <form class="layui-form p-page">
                {if condition="$id > 0"}
                <input type="hidden" name="id" value="{$id}" />
                {/if}
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">物料编号<font>*</font></td>
                        <td>
                            <input type="text" name="material_code" value="{$detail.material_code ?? ''}" lay-verify="required" lay-reqText="请输入物料编号" autocomplete="off" placeholder="请输入物料编号" class="layui-input" id="material_code_input">
                            <div style="margin-top: 5px;">
                                <input type="checkbox" name="use_system_code" id="use_system_code" title="系统自动编号" lay-skin="primary" lay-filter="use_system_code">
                                <label for="use_system_code" style="margin-left: 5px; font-size: 12px; color: #666;">系统自动编号</label>
                            </div>
                        </td>
                        <td class="layui-td-gray">物料名称<font>*</font></td>
                        <td><input type="text" name="title" value="{$detail.title ?? ''}" lay-verify="required" lay-reqText="请输入物料名称" autocomplete="off" placeholder="请输入物料名称" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料分类<font>*</font></td>
                        <td>
                            <select name="cate_id" lay-verify="required" lay-reqText="请选择物料分类">
                                <option value="">请选择物料分类</option>
                                {volist name=":set_recursion(get_base_data('ProductCate'))" id="v"}
                                <option value="{$v.id}" {if condition="isset($detail['cate_id']) && $detail['cate_id'] == $v.id"}selected{/if}>{$v.title}</option>
                                {/volist}
                            </select>
                        </td>
                        <td class="layui-td-gray">基本单位<font>*</font></td>
                        <td>
                            <select name="unit" lay-verify="required" lay-reqText="请选择基本单位">
                                <option value="">请选择基本单位</option>
                                {volist name="units" id="unit"}
                                <option value="{$unit.name}" {if condition="isset($detail['unit']) && $detail['unit'] == $unit.name"}selected{/if}>{$unit.name}</option>
                                {/volist}
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料规格</td>
                        <td><input type="text" name="specs" value="{$detail.specs ?? ''}" autocomplete="off" placeholder="请输入物料规格" class="layui-input"></td>
                        <td class="layui-td-gray">物料类型<font>*</font></td>
                        <td>
                            <input type="radio" name="source_type" value="1" title="自产" {if condition="!isset($detail['source_type']) || $detail['source_type'] == 1"}checked{/if}>
                            <input type="radio" name="source_type" value="2" title="外购" {if condition="isset($detail['source_type']) && $detail['source_type'] == 2"}checked{/if}>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">状态</td>
                        <td>
                            <input type="radio" name="status" value="1" title="启用" {if condition="!isset($detail['status']) || $detail['status'] == 1"}checked{/if}>
                            <input type="radio" name="status" value="0" title="禁用" {if condition="isset($detail['status']) && $detail['status'] == 0"}checked{/if}>
                        </td>
                        <td class="layui-td-gray">生产商</td>
                        <td><input type="text" name="producer" value="{$detail.producer ?? ''}" autocomplete="off" placeholder="请输入生产商" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">采购周期(天)</td>
                        <td><input type="number" name="purchase_cycle" value="{$detail.purchase_cycle ?? ''}" autocomplete="off" placeholder="请输入采购周期" class="layui-input"></td>
                        <td class="layui-td-gray">库存阙值</td>
                        <td><input type="number" name="stock" value="{$detail.stock ?? ''}" autocomplete="off" placeholder="请输入库存数量" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">默认仓库</td>
                        <td>
                            <select name="default_warehouse">
                                <option value="">请选择仓库</option>
                                {volist name="warehouses" id="warehouse"}
                                <option value="{$warehouse.id}" {if condition="isset($detail['default_warehouse']) && $detail['default_warehouse'] == $warehouse.id"}selected{/if}>{$warehouse.name}</option>
                                {/volist}
                            </select>
                        </td>
                        <td class="layui-td-gray">最小起订量</td>
                        <td><input type="number" name="min_order_qty" value="{$detail.min_order_qty ?? ''}" placeholder="请输入最小起订量" class="layui-input" step="0.01"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">最小包装量</td>
                        <td><input type="number" name="min_package_qty" value="{$detail.min_package_qty ?? ''}" placeholder="请输入最小包装量" class="layui-input" step="0.01"></td>
                        <td class="layui-td-gray">物料等级</td>
                        <td>
                            <select name="material_level">
                                <option value="">请选择</option>
                                <option value="A" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'A'"}selected{/if}>A</option>
                                <option value="B" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'B'"}selected{/if}>B</option>
                                <option value="C" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'C'"}selected{/if}>C</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料来源</td>
                        <td>
                            <select name="material_source">
                                <option value="自购" {if condition="isset($detail['material_source']) && $detail['material_source'] == '自购'"}selected{/if}>自购</option>
                                <option value="加工" {if condition="isset($detail['material_source']) && $detail['material_source'] == '加工'"}selected{/if}>加工</option>
                                <option value="客供" {if condition="isset($detail['material_source']) && $detail['material_source'] == '客供'"}selected{/if}>客供</option>
                                <option value="委外供料" {if condition="isset($detail['material_source']) && $detail['material_source'] == '委外供料'"}selected{/if}>委外供料</option>
                            </select>
                        </td>
                        <td class="layui-td-gray">物料类别</td>
                        <td>
                            <select name="category">
                                <option value="">请选择</option>
                                <option value="主料" {if condition="isset($detail['category']) && $detail['category'] == '主料'"}selected{/if}>主料</option>
                                <option value="辅料" {if condition="isset($detail['category']) && $detail['category'] == '辅料'"}selected{/if}>辅料</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">型号</td>
                        <td><input type="text" name="model" value="{$detail.model ?? ''}" placeholder="请输入型号" class="layui-input"></td>
                        <td class="layui-td-gray">颜色</td>
                        <td><input type="text" name="color" value="{$detail.color ?? ''}" placeholder="请输入颜色" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">物料描述</td>
                        <td colspan="3"><textarea name="description" placeholder="请输入物料描述" class="layui-textarea">{$detail.description ?? ''}</textarea></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">备注信息</td>
                        <td colspan="3"><textarea name="remark" placeholder="请输入备注信息" class="layui-textarea">{$detail.remark ?? ''}</textarea></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">物料图片</td>
                        <td colspan="3">
                            <button type="button" class="layui-btn" id="uploadMaterialImages">
                                <i class="layui-icon">&#xe67c;</i>上传图片
                            </button>
                            <div class="layui-form-mid layui-word-aux">最多上传6张图片,支持JPG,PNG,BMP格式，最大不超过5M</div>
                            <div id="materialImagesList" style="margin-top: 10px;"></div>
                        </td>
                    </tr>
                 
                </table>
            </form>
        </div>
        <!-- 质检信息 -->
        <div class="layui-tab-item">
            <form class="layui-form p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">质检管理</td>
                        <td>
                            <input type="checkbox" name="quality_management" value="1" lay-skin="switch" lay-text="启用|禁用" lay-filter="quality_management" {if condition="isset($detail['quality_management']) && $detail['quality_management'] == 1"}checked{/if}>
                        </td>
                        <td class="layui-td-gray">免检设置</td>
                        <td>
                            <input type="checkbox" name="quality_exempt" value="1" lay-skin="switch" lay-text="关闭|开启" lay-filter="quality_exempt" {if condition="isset($detail['quality_exempt']) && $detail['quality_exempt'] == 1"}checked{/if}>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">质检项目</td>
                        <td colspan="3">
                            <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                                <div>
                                    <input type="checkbox" name="quality_settings[]" value="purchase_inspect" title="采购入库检" {if condition="isset($detail['quality_settings']) && in_array('purchase_inspect', $detail['quality_settings'])"}checked{/if}>
                                </div>
                                <div>
                                    <input type="checkbox" name="quality_settings[]" value="outsource_inspect" title="外协入库检" {if condition="isset($detail['quality_settings']) && in_array('outsource_inspect', $detail['quality_settings'])"}checked{/if}>
                                </div>
                                <div>
                                    <input type="checkbox" name="quality_settings[]" value="production_inspect" title="生产入库检" {if condition="isset($detail['quality_settings']) && in_array('production_inspect', $detail['quality_settings'])"}checked{/if}>
                                </div>
                                <div>
                                    <input type="checkbox" name="quality_settings[]" value="sales_inspect" title="销售出库检" {if condition="isset($detail['quality_settings']) && in_array('sales_inspect', $detail['quality_settings'])"}checked{/if}>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </form>
        </div>



        <!-- 价格信息 -->
        <div class="layui-tab-item">
            <form class="layui-form p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">参考成本价<font>*</font></td>
                        <td><input type="number" name="reference_cost" value="{$detail.reference_cost ?? ''}" placeholder="请输入参考成本价" class="layui-input" step="0.01"></td>
                        <td class="layui-td-gray">销售单价（含税）</td>
                        <td><input type="number" name="sales_price" value="{$detail.sales_price ?? ''}" placeholder="请输入销售单价" class="layui-input" step="0.01"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">最低销售单价</td>
                        <td><input type="number" name="min_sales_price" value="{$detail.min_sales_price ?? ''}" placeholder="请输入最低销售单价" class="layui-input" step="0.01"></td>
                        <td class="layui-td-gray">最高销售单价</td>
                        <td><input type="number" name="max_sales_price" value="{$detail.max_sales_price ?? ''}" placeholder="请输入最高销售单价" class="layui-input" step="0.01"></td>
                    </tr>
                   
                </table>
            </form>
        </div>



        <!-- 工艺管理 -->
        <div class="layui-tab-item">
            <div class="layui-tab layui-tab-brief" lay-filter="processTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">供应商价格</li>
                    <li>外协价格</li>
                    <li>附件管理</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 供应商价格 -->
                    <div class="layui-tab-item layui-show">
                        <form class="layui-form p-page">
                            <div style="margin-bottom: 15px;">
                                <button type="button" class="layui-btn layui-btn-sm" onclick="addSupplierPriceRow()">
                                    <i class="layui-icon layui-icon-add-1"></i> 新增供应商
                                </button>
                            </div>
                            <table class="layui-table" lay-skin="line" id="supplierPriceTable">
                                <thead>
                                    <tr>
                                        <th>供应商名称</th>
                                        <th>供应商编号</th>
                                        <th>采购优先级</th>
                                        <th>采购下限</th>
                                        <th>采购上限</th>
                                        <th>含税单价</th>
                                        <th>税率(%)</th>
                                        <th>不含税单价</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="supplierPriceTableBody">
                                    <tr class="empty-row">
                                        <td colspan="9" style="text-align: center; color: #999; padding: 30px;">
                                            暂无供应商数据
                                            <br><br>
                                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addSupplierPriceRow()">
                                                <i class="layui-icon layui-icon-add-1"></i> 添加供应商
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </form>
                    </div>

                    <!-- 外协价格 -->
                    <div class="layui-tab-item">
                        <form class="layui-form p-page">
                            <div style="margin-bottom: 15px;">
                                <button type="button" class="layui-btn layui-btn-sm" onclick="addOutsourcePriceRow()">
                                    <i class="layui-icon layui-icon-add-1"></i> 新增外协商
                                </button>
                            </div>
                            <table class="layui-table" lay-skin="line" id="outsourcePriceTable">
                                <thead>
                                    <tr>
                                        <th>外协商名称</th>
                                        <th>外协商编号</th>
                                        <th>外协优先级</th>
                                        <th>外协下限</th>
                                        <th>外协上限</th>
                                        <th>含税单价</th>
                                        <th>税率(%)</th>
                                        <th>不含税单价</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="outsourcePriceTableBody">
                                    <tr class="empty-row">
                                        <td colspan="9" style="text-align: center; color: #999; padding: 30px;">
                                            暂无外协商数据
                                            <br><br>
                                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addOutsourcePriceRow()">
                                                <i class="layui-icon layui-icon-add-1"></i> 添加外协商
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </form>
                    </div>

                    <!-- 附件管理 -->
                    <div class="layui-tab-item">
                        <form class="layui-form p-page">
                            <table class="layui-table layui-table-form">
                                <tr>
                                    <td class="layui-td-gray" style="vertical-align:top">附件上传</td>
                                    <td colspan="3">
                                        <button type="button" class="layui-btn" id="uploadBtn">
                                            <i class="layui-icon">&#xe67c;</i>上传附件
                                        </button>
                                        <div class="layui-form-mid layui-word-aux">支持上传多个文件，最大不超过10M</div>
                                        <div id="attachmentList" style="margin-top: 10px;"></div>
                                    </td>
                                </tr>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮区域 -->
<div style="text-align: center; padding: 20px; border-top: 1px solid #e8e8e8; background: #fafafa;">
    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="addSubmit" id="submitBtn">
        <i class="layui-icon layui-icon-ok"></i> 保存
    </button>
    <button type="reset" class="layui-btn layui-btn-primary" style="margin-left: 10px;">
        <i class="layui-icon layui-icon-refresh"></i> 重置
    </button>
    <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()" style="margin-left: 10px;">
        <i class="layui-icon layui-icon-close"></i> 取消
    </button>
</div>

{/block}

{block name="script"}
<script>
const moduleInit = ['tool','form','laydate','upload','element'];
function gouguInit() {
    var form = layui.form, tool = layui.tool, laydate = layui.laydate, upload = layui.upload, element = layui.element;

    // 系统自动编号功能
    form.on('checkbox(use_system_code)', function(data){
        var isChecked = data.elem.checked;
        var materialCodeInput = $('#material_code_input');

        if(isChecked){
            materialCodeInput.val('AUTO_GENERATE');
            materialCodeInput.prop('readonly', true);
            materialCodeInput.removeAttr('lay-verify');
            materialCodeInput.css({
                'background-color': '#f5f5f5',
                'color': '#999',
                'cursor': 'not-allowed'
            });
        } else {
            materialCodeInput.val('');
            materialCodeInput.prop('readonly', false);
            materialCodeInput.attr('lay-verify', 'required');
            materialCodeInput.css({
                'background-color': '',
                'color': '',
                'cursor': ''
            });
        }
    });

    // 质检管理开关
    form.on('switch(quality_management)', function(data){
        var isChecked = data.elem.checked;
        if(isChecked){
            $('input[name="quality_settings[]"]').prop('checked', true);
            $('input[name="quality_exempt"]').prop('checked', false);
            form.render();
        } else {
            $('input[name="quality_settings[]"]').prop('checked', false);
            $('input[name="quality_exempt"]').prop('checked', false);
            form.render();
        }
    });

    // 免检设置开关
    form.on('switch(quality_exempt)', function(data){
        var isChecked = data.elem.checked;
        if(isChecked){
            $('input[name="quality_settings[]"]').prop('checked', false);
            form.render('checkbox');
        } else {
            $('input[name="quality_settings[]"]').prop('checked', true);
            form.render('checkbox');
        }
    });

    // 图片上传
    upload.render({
        elem: '#uploadMaterialImages',
        url: '/upload/image',
        multiple: true,
        accept: 'images',
        acceptMime: 'image/*',
        number: 6,
        done: function(res){
            if(res.code == 0){
                var html = '<div class="upload-item">' +
                    '<img src="' + res.data.url + '" width="100" height="100">' +
                    '<input type="hidden" name="material_images[]" value="' + res.data.url + '">' +
                    '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeUploadItem(this)">删除</button>' +
                    '</div>';
                $('#materialImagesList').append(html);
                layer.msg('上传成功');
            } else {
                layer.msg('上传失败：' + res.msg);
            }
        }
    });

    // 图纸上传
    upload.render({
        elem: '#uploadMaterialDrawing',
        url: '/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        done: function(res){
            if(res.code == 0){
                var html = '<div class="upload-item">' +
                    '<img src="' + res.data.url + '" width="100" height="100">' +
                    '<input type="hidden" name="material_drawing" value="' + res.data.url + '">' +
                    '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeUploadItem(this)">删除</button>' +
                    '</div>';
                $('#materialDrawingList').html(html);
                layer.msg('上传成功');
            } else {
                layer.msg('上传失败：' + res.msg);
            }
        }
    });

    // 附件上传
    upload.render({
        elem: '#uploadBtn',
        url: '/upload/file',
        accept: 'file',
        multiple: true,
        done: function(res){
            if(res.code == 0){
                var html = '<div class="upload-item">' +
                    '<a href="' + res.data.url + '" target="_blank">' + res.data.name + '</a>' +
                    '<input type="hidden" name="attachments[]" value="' + res.data.url + '">' +
                    '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeUploadItem(this)">删除</button>' +
                    '</div>';
                $('#attachmentList').append(html);
                layer.msg('上传成功');
            } else {
                layer.msg('上传失败：' + res.msg);
            }
        }
    });

    // 删除上传项
    window.removeUploadItem = function(btn) {
        $(btn).closest('.upload-item').remove();
    };

    // 供应商数据
    var suppliersData = [];
    {if condition="$suppliers"}
    {volist name="suppliers" id="supplier"}
    suppliersData.push({
        id: '{$supplier.id}',
        code: '{$supplier.code}',
        name: '{$supplier.name}'
    });
    {/volist}
    {/if}

    // 新增供应商价格行
    window.addSupplierPriceRow = function() {
        var tbody = $('#supplierPriceTableBody');
        var emptyRow = tbody.find('.empty-row');

        if(emptyRow.length > 0) {
            emptyRow.remove();
        }

        var rowIndex = tbody.find('tr').length;
        var supplierOptions = '';

        for(var i = 0; i < suppliersData.length; i++) {
            if(suppliersData[i].id) {
                supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '">' + suppliersData[i].name + '</option>';
            }
        }

        var newRow = '<tr>' +
            '<td><select name="supplier_prices[' + rowIndex + '][supplier_id]" class="layui-input" onchange="updateSupplierInfo(this, ' + rowIndex + ')"><option value="">请选择供应商</option>' + supplierOptions + '</select></td>' +
            '<td><input type="text" name="supplier_prices[' + rowIndex + '][supplier_code]" class="layui-input" readonly></td>' +
            '<td><select name="supplier_prices[' + rowIndex + '][priority]" class="layui-input"><option value="">请选择</option><option value="首选">首选</option><option value="备选">备选</option></select></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][min_qty]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][max_qty]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][tax_price]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][tax_rate]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][no_tax_price]" class="layui-input" step="0.01"></td>' +
            '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeSupplierPriceRow(this)">删除</button></td>' +
            '</tr>';

        tbody.append(newRow);
        form.render('select');
    };

    // 新增外协价格行
    window.addOutsourcePriceRow = function() {
        var tbody = $('#outsourcePriceTableBody');
        var emptyRow = tbody.find('.empty-row');

        if(emptyRow.length > 0) {
            emptyRow.remove();
        }

        var rowIndex = tbody.find('tr').length;
        var supplierOptions = '';

        for(var i = 0; i < suppliersData.length; i++) {
            if(suppliersData[i].id) {
                supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '">' + suppliersData[i].name + '</option>';
            }
        }

        var newRow = '<tr>' +
            '<td><select name="outsource_prices[' + rowIndex + '][supplier_id]" class="layui-input" onchange="updateOutsourceInfo(this, ' + rowIndex + ')"><option value="">请选择外协商</option>' + supplierOptions + '</select></td>' +
            '<td><input type="text" name="outsource_prices[' + rowIndex + '][outsource_code]" class="layui-input" readonly></td>' +
            '<td><select name="outsource_prices[' + rowIndex + '][priority]" class="layui-input"><option value="">请选择</option><option value="首选">首选</option><option value="备选">备选</option></select></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][min_qty]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][max_qty]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][tax_price]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][tax_rate]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][no_tax_price]" class="layui-input" step="0.01"></td>' +
            '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeOutsourcePriceRow(this)">删除</button></td>' +
            '</tr>';

        tbody.append(newRow);
        form.render('select');
    };

    // 更新供应商信息
    window.updateSupplierInfo = function(selectElement, rowIndex) {
        var selectedOption = $(selectElement).find('option:selected');
        var supplierCode = selectedOption.data('code');
        $(selectElement).closest('tr').find('input[name="supplier_prices[' + rowIndex + '][supplier_code]"]').val(supplierCode || '');
    };

    // 更新外协商信息
    window.updateOutsourceInfo = function(selectElement, rowIndex) {
        var selectedOption = $(selectElement).find('option:selected');
        var supplierCode = selectedOption.data('code');
        $(selectElement).closest('tr').find('input[name="outsource_prices[' + rowIndex + '][outsource_code]"]').val(supplierCode || '');
    };

    // 删除供应商价格行
    window.removeSupplierPriceRow = function(btn) {
        var tbody = $('#supplierPriceTableBody');
        $(btn).closest('tr').remove();

        if(tbody.find('tr').length === 0) {
            tbody.append('<tr class="empty-row"><td colspan="9" style="text-align: center; color: #999; padding: 30px;">暂无供应商数据<br><br><button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addSupplierPriceRow()">添加供应商</button></td></tr>');
        }
    };

    // 删除外协价格行
    window.removeOutsourcePriceRow = function(btn) {
        var tbody = $('#outsourcePriceTableBody');
        $(btn).closest('tr').remove();

        if(tbody.find('tr').length === 0) {
            tbody.append('<tr class="empty-row"><td colspan="9" style="text-align: center; color: #999; padding: 30px;">暂无外协商数据<br><br><button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addOutsourcePriceRow()">添加外协商</button></td></tr>');
        }
    };

    // 表单提交
    form.on('submit(addSubmit)', function(data){
        // 收集所有标签页的表单数据
        var submitData = {};

        // 收集所有表单字段
        $('.layui-tab-content form').each(function() {
            $(this).find('input, select, textarea').each(function() {
                var $this = $(this);
                var name = $this.attr('name');
                var value = $this.val();
                var type = $this.attr('type');

                if (name && !$this.prop('disabled')) {
                    if (name.indexOf('supplier_prices[') === 0 || name.indexOf('outsource_prices[') === 0) {
                        return;
                    }

                    if (type === 'checkbox') {
                        if (name.indexOf('[]') > -1) {
                            if (!submitData[name]) submitData[name] = [];
                            if ($this.is(':checked')) {
                                submitData[name].push(value);
                            }
                        } else {
                            if ($this.is(':checked')) {
                                submitData[name] = value;
                            }
                            // 未选中的复选框不设置字段，让后端通过isset判断
                        }
                    } else if (type === 'radio') {
                        if ($this.is(':checked')) {
                            submitData[name] = value;
                        }
                    } else {
                        submitData[name] = value || '';
                    }
                }
            });
        });

        // 收集供应商价格数据
        var supplierPrices = [];
        $('#supplierPriceTableBody tr').each(function() {
            if ($(this).hasClass('empty-row')) return;

            var rowData = {};
            var hasData = false;

            $(this).find('input, select').each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();

                if (name && value) {
                    if (name.indexOf('supplier_prices[') === 0) {
                        var start = name.indexOf('[', name.indexOf('[') + 1) + 1;
                        var end = name.lastIndexOf(']');
                        if (start > 0 && end > start) {
                            var fieldName = name.substring(start, end);
                            rowData[fieldName] = value;
                            hasData = true;
                        }
                    }
                }
            });

            if (hasData) {
                supplierPrices.push(rowData);
            }
        });

        if (supplierPrices.length > 0) {
            submitData['supplier_prices'] = supplierPrices;
        }

        // 收集外协价格数据
        var outsourcePrices = [];
        $('#outsourcePriceTableBody tr').each(function() {
            if ($(this).hasClass('empty-row')) return;

            var rowData = {};
            var hasData = false;

            $(this).find('input, select').each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();

                if (name && value) {
                    if (name.indexOf('outsource_prices[') === 0) {
                        var start = name.indexOf('[', name.indexOf('[') + 1) + 1;
                        var end = name.lastIndexOf(']');
                        if (start > 0 && end > start) {
                            var fieldName = name.substring(start, end);
                            rowData[fieldName] = value;
                            hasData = true;
                        }
                    }
                }
            });

            if (hasData) {
                outsourcePrices.push(rowData);
            }
        });

        if (outsourcePrices.length > 0) {
            submitData['outsource_prices'] = outsourcePrices;
        }

        let callback = function (e) {
            layer.msg(e.msg);
            if (e.code == 0) {
                parent.layui.pageTable.reload();
                parent.layer.closeAll();
            }
        }

        tool.post("/material/archive/add", submitData, callback);
        return false;
    });

    // 标签页切换事件
    element.on('tab(materialTab)', function(data){
        console.log('切换到标签页：', data.index);
    });

    element.on('tab(processTab)', function(data){
        console.log('切换到工艺管理子标签页：', data.index);
    });

    // 初始化现有数据（编辑模式）
    initExistingData();

    // 渲染表单
    form.render();
}
</script>

<script>
// 数据变量
var supplierPricesData = {$supplier_prices|default='[]'|json_encode|raw};
var outsourcePricesData = {$outsource_prices|default='[]'|json_encode|raw};
var suppliersData = {$suppliers|default='[]'|json_encode|raw};

// 初始化现有数据
function initExistingData() {
    // 加载供应商价格数据
    if (supplierPricesData && supplierPricesData.length > 0) {
        $('#supplierPriceTableBody .empty-row').remove();
        for (var i = 0; i < supplierPricesData.length; i++) {
            var price = supplierPricesData[i];
            addSupplierPriceRowWithData(price, i);
        }
    }

    // 加载外协价格数据
    if (outsourcePricesData && outsourcePricesData.length > 0) {
        $('#outsourcePriceTableBody .empty-row').remove();
        for (var i = 0; i < outsourcePricesData.length; i++) {
            var price = outsourcePricesData[i];
            addOutsourcePriceRowWithData(price, i);
        }
    }
}

// 添加供应商价格行（带数据）
function addSupplierPriceRowWithData(priceData, rowIndex) {
    var tbody = $('#supplierPriceTableBody');

    var supplierOptions = '<option value="">请选择供应商</option>';
    if (suppliersData && suppliersData.length > 0) {
        for (var i = 0; i < suppliersData.length; i++) {
            var selected = (suppliersData[i].id == priceData.supplier_id) ? 'selected' : '';
            supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '" ' + selected + '>' + suppliersData[i].name + '</option>';
        }
    }

    var newRow = '<tr>' +
        '<td><select name="supplier_prices[' + rowIndex + '][supplier_id]" class="layui-input" onchange="updateSupplierInfo(this, ' + rowIndex + ')">' + supplierOptions + '</select></td>' +
        '<td><input type="text" name="supplier_prices[' + rowIndex + '][supplier_code]" class="layui-input" readonly value="' + (priceData.supplier_code || '') + '"></td>' +
        '<td><select name="supplier_prices[' + rowIndex + '][priority]" class="layui-input"><option value="">请选择</option><option value="首选" ' + (priceData.priority == '首选' ? 'selected' : '') + '>首选</option><option value="备选" ' + (priceData.priority == '备选' ? 'selected' : '') + '>备选</option></select></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][min_qty]" class="layui-input" step="0.01" value="' + (priceData.min_qty || '') + '"></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][max_qty]" class="layui-input" step="0.01" value="' + (priceData.max_qty || '') + '"></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][tax_price]" class="layui-input" step="0.01" value="' + (priceData.tax_price || '') + '"></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][tax_rate]" class="layui-input" step="0.01" value="' + (priceData.tax_rate || '') + '"></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][no_tax_price]" class="layui-input" step="0.01" value="' + (priceData.no_tax_price || '') + '"></td>' +
        '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeSupplierPriceRow(this)">删除</button></td>' +
        '</tr>';

    tbody.append(newRow);
    layui.form.render('select');
}

// 添加外协价格行（带数据）
function addOutsourcePriceRowWithData(priceData, rowIndex) {
    var tbody = $('#outsourcePriceTableBody');

    var supplierOptions = '<option value="">请选择外协商</option>';
    if (suppliersData && suppliersData.length > 0) {
        for (var i = 0; i < suppliersData.length; i++) {
            var selected = (suppliersData[i].id == priceData.supplier_id) ? 'selected' : '';
            supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '" ' + selected + '>' + suppliersData[i].name + '</option>';
        }
    }

    var newRow = '<tr>' +
        '<td><select name="outsource_prices[' + rowIndex + '][supplier_id]" class="layui-input" onchange="updateOutsourceInfo(this, ' + rowIndex + ')">' + supplierOptions + '</select></td>' +
        '<td><input type="text" name="outsource_prices[' + rowIndex + '][outsource_code]" class="layui-input" readonly value="' + (priceData.outsource_code || '') + '"></td>' +
        '<td><select name="outsource_prices[' + rowIndex + '][priority]" class="layui-input"><option value="">请选择</option><option value="首选" ' + (priceData.priority == '首选' ? 'selected' : '') + '>首选</option><option value="备选" ' + (priceData.priority == '备选' ? 'selected' : '') + '>备选</option></select></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][min_qty]" class="layui-input" step="0.01" value="' + (priceData.min_qty || '') + '"></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][max_qty]" class="layui-input" step="0.01" value="' + (priceData.max_qty || '') + '"></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][tax_price]" class="layui-input" step="0.01" value="' + (priceData.tax_price || '') + '"></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][tax_rate]" class="layui-input" step="0.01" value="' + (priceData.tax_rate || '') + '"></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][no_tax_price]" class="layui-input" step="0.01" value="' + (priceData.no_tax_price || '') + '"></td>' +
        '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeOutsourcePriceRow(this)">删除</button></td>' +
        '</tr>';

    tbody.append(newRow);
    layui.form.render('select');
}
</script>
{/block}
