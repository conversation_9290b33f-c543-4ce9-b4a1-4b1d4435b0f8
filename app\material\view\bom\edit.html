{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.form-container {
    padding: 20px;
    background: #fff;
}
.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1E9FFF;
}
.material-table {
    margin-top: 20px;
}
.material-table .layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.btn-group {
    margin-top: 20px;
    text-align: center;
}
.required {
    color: #FF5722;
}
/* 多级BOM样式 */
.sub-bom-row {
    background-color: #f8f9fa;
}
.sub-bom-row:hover {
    background-color: #e9ecef;
}
/* BOM等级缩进样式 */
.bom-level-1 { padding-left: 0px; }
.bom-level-2 { padding-left: 20px; }
.bom-level-3 { padding-left: 40px; }
.bom-level-4 { padding-left: 60px; }
.bom-level-5 { padding-left: 80px; }
</style>
{/block}

<!-- 主体 -->
{block name="body"}
<div class="form-container">
    <form class="layui-form" lay-filter="bomForm" id="bomForm">
        <input type="hidden" name="id" value="{$bom.id|default=0}" />
        <input type="hidden" name="product_id" value="{$bom.product_id|default=0}" />
        <input type="hidden" name="customer_id" value="{$bom.customer_id|default=0}" />

        <!-- 基础信息 -->
        <div class="section-title">基础信息</div>
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>BOM编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="bom_code" value="{$bom.bom_code|default=$bom_code}" lay-verify="required" placeholder="请输入BOM编号" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>BOM名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="bom_name" value="{$bom.bom_name|default=''}" lay-verify="required" placeholder="请输入BOM名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>产品名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="product_name" value="{$bom.product_name|default=''}" lay-verify="required" placeholder="产品名称" autocomplete="off" class="layui-input" readonly style="background-color: #f5f5f5; cursor: not-allowed;">
                        <div class="layui-form-mid layui-word-aux">编辑时不可修改产品</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">客户名称</label>
                    <div class="layui-input-block">
                        <div class="layui-input-group">
                            <input type="text" name="customer_name" value="{$bom.customer_name|default=''}" placeholder="请选择客户" autocomplete="off" class="layui-input" readonly>
                            <div class="layui-input-split layui-input-suffix" style="cursor: pointer;" onclick="selectCustomer()">
                                <i class="layui-icon layui-icon-search"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入备注" class="layui-textarea">{$bom.remark|default=''}</textarea>
            </div>
        </div>

        <!-- 物料清单 -->
        <div class="section-title">物料清单</div>
        <div class="material-table">
            <div style="margin-bottom: 10px;">
                <button type="button" class="layui-btn layui-btn-sm" onclick="addMaterial()">
                    <i class="layui-icon layui-icon-add-1"></i> 添加物料
                </button>
                <button type="button" class="layui-btn layui-btn-warm layui-btn-sm" onclick="importBom()">
                    <i class="layui-icon layui-icon-upload"></i> 导入BOM表
                </button>
                <button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="deleteMaterials()">
                    <i class="layui-icon layui-icon-delete"></i> 删除选中
                </button>
            </div>

            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th width="50"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                        <th width="60">BOM等级</th>
                        <th width="120">物料编号</th>
                        <th width="150">物料名称</th>
                        <th width="60">图片</th>
                        <th width="80">物料分类</th>
                        <th width="120">规格</th>
                        <th width="80">物料来源</th>
                        <th width="80">用量</th>
                        <th width="60">单位</th>
                        <th width="80">损耗率(%)</th>
                        <th width="60">下级BOM</th>
                        <th width="80">操作</th>
                    </tr>
                </thead>
                <tbody id="materialTableBody">
                    <tr class="empty-row" style="display: none;">
                        <td colspan="13" style="text-align: center; color: #999;">暂无物料数据</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 操作按钮 -->
        <div class="btn-group">
            <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="bomSubmit">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">取消</button>
        </div>
    </form>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var form = layui.form, tool = layui.tool;

    // 添加物料行（带数据）
    window.addMaterialRowWithData = function(material) {
        // 隐藏空行提示
        $('#materialTableBody .empty-row').hide();

        var levelClass = 'bom-level-' + (material.bom_level || 1);
        var html = '<tr data-material-id="' + material.material_id + '" data-level="' + (material.bom_level || 1) + '" data-parent="' + (material.parent_material_id || 0) + '" class="' + (material.bom_level > 1 ? 'sub-bom-row' : '') + '">';
        html += '<td><input type="checkbox" lay-skin="primary"></td>';
        html += '<td class="' + levelClass + '">' + (material.bom_level || 1) + '</td>';
        html += '<td>' + (material.material_code || '') + '</td>';
        html += '<td>' + (material.material_name || '') + '</td>';
        html += '<td><img src="/static/images/default.png" style="width:40px;height:40px;"></td>';
        html += '<td>' + (material.material_category || '') + '</td>';
        html += '<td>' + (material.specifications || '') + '</td>';
        html += '<td><select name="material_source" lay-verify="required" style="width:100%;">';
        html += '<option value="自购"' + (material.material_source == '自购' ? ' selected' : '') + '>自购</option>';
        html += '<option value="自制"' + (material.material_source == '自制' ? ' selected' : '') + '>自制</option>';
        html += '<option value="委外"' + (material.material_source == '委外' ? ' selected' : '') + '>委外</option>';
        html += '</select></td>';
        html += '<td><input type="number" name="quantity" value="' + (material.quantity || '1.0000') + '" class="layui-input" style="width:100%;" step="0.0001" min="0" placeholder="用量"></td>';
        html += '<td><input type="text" name="unit" value="' + (material.unit || '') + '" class="layui-input" style="width:100%;" placeholder="单位"></td>';
        html += '<td><input type="number" name="loss_rate" value="' + (material.loss_rate || '0.00') + '" class="layui-input" style="width:100%;" step="0.01" min="0" max="100" placeholder="损耗率"></td>';
        html += '<td><button type="button" class="layui-btn layui-btn-xs" onclick="expandBom(this)">展开</button></td>';
        html += '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeMaterial(this)">删除</button></td>';
        html += '</tr>';

        $('#materialTableBody').append(html);
        layui.form.render('select');
    };

    // 初始化已有物料数据
    {volist name="materials" id="material"}
    addMaterialRowWithData({
        material_id: {$material.material_id|default=0},
        material_code: '{$material.material_code|default=""}',
        material_name: '{$material.material_name|default=""}',
        material_category: '{$material.material_category|default=""}',
        specifications: '{$material.specifications|default=""}',
        material_source: '{$material.material_source|default="自购"}',
        quantity: '{$material.quantity|default="1.0000"}',
        unit: '{$material.unit|default=""}',
        loss_rate: '{$material.loss_rate|default="0.00"}',
        bom_level: {$material.bom_level|default=1},
        parent_material_id: {$material.parent_material_id|default=0}
    });
    {/volist}

    // 如果没有物料数据，显示空行提示
    {empty name="materials"}
    $('#materialTableBody .empty-row').show();
    {/empty}

    // 选择产品 - 编辑时禁用
    window.selectProduct = function() {
        layer.msg('编辑时不可修改产品', {icon: 2});
        return false;
    };

    // 选择客户
    window.selectCustomer = function() {
        layer.open({
            type: 2,
            title: '选择客户',
            shadeClose: true,
            shade: 0.3,
            maxmin: true,
            area: ['900px', '600px'],
            content: '/material/bom/selectCustomer'
        });
    };

    // 添加物料
    window.addMaterial = function() {
        if (!$('input[name="product_id"]').val()) {
            layer.msg('请先选择产品', {icon: 2});
            return;
        }

        var excludeProduct = $('input[name="product_id"]').val();
        layer.open({
            type: 2,
            title: '选择物料',
            shadeClose: true,
            shade: 0.3,
            maxmin: true,
            area: ['1200px', '700px'],
            content: '/material/bom/getMaterials?exclude_product=' + excludeProduct
        });
    };



    // 删除物料
    window.removeMaterial = function(btn) {
        $(btn).closest('tr').remove();
        // 如果没有物料了，显示空行提示
        if ($('#materialTableBody tr:not(.empty-row)').length === 0) {
            $('#materialTableBody .empty-row').show();
        }
    };

    // 展开BOM
    window.expandBom = function(btn) {
        var row = $(btn).closest('tr');
        var materialId = row.data('material-id');

        if (!materialId) {
            layer.msg('物料ID不存在', {icon: 2});
            return;
        }

        $.get('/material/bom/getSubBom', {material_id: materialId}, function(res){
            if (res.code == 0 && res.data.length > 0) {
                res.data.forEach(function(subMaterial) {
                    addMaterialRowWithData(subMaterial);
                });
                layer.msg('展开成功', {icon: 1});
            } else {
                layer.msg('该物料没有下级BOM', {icon: 0});
            }
        });
    };

    // 删除选中物料
    window.deleteMaterials = function() {
        var checkedRows = $('#materialTableBody input[type="checkbox"]:checked').closest('tr');
        if (checkedRows.length === 0) {
            layer.msg('请选择要删除的物料', {icon: 2});
            return;
        }

        layer.confirm('确定删除选中的物料吗？', {icon: 3, title: '提示'}, function(index) {
            checkedRows.remove();
            layer.close(index);
            layer.msg('删除成功', {icon: 1});

            // 如果没有物料了，显示空行提示
            if ($('#materialTableBody tr:not(.empty-row)').length === 0) {
                $('#materialTableBody .empty-row').show();
            }
        });
    };

    // 导入BOM表
    window.importBom = function() {
        layer.msg('BOM导入功能待开发');
    };

    // 全选
    form.on('checkbox(allChoose)', function(data) {
        var child = $('#materialTableBody input[type="checkbox"]');
        child.each(function(index, item) {
            item.checked = data.elem.checked;
        });
        form.render();
    });

    // 表单提交
    form.on('submit(bomSubmit)', function(data) {
        // 收集物料数据
        var materials = [];
        $('#materialTableBody tr').each(function() {
            if ($(this).hasClass('empty-row')) return;

            var materialData = {
                material_id: $(this).data('material-id') || $(this).data('id'), // 物料ID
                material_code: $(this).find('td:eq(2)').text(),
                material_name: $(this).find('td:eq(3)').text(),
                material_category: $(this).find('td:eq(5)').text(),
                specifications: $(this).find('td:eq(6)').text(),
                material_source: $(this).find('select[name*="material_source"]').val(),
                quantity: parseFloat($(this).find('input[name="quantity"]').val()) || 1.0000, // 用量
                unit: $(this).find('input[name="unit"]').val() || '', // 单位
                loss_rate: parseFloat($(this).find('input[name="loss_rate"]').val()) || 0.00, // 损耗率
                bom_level: $(this).data('level') || 1, // BOM等级
                parent_material_id: $(this).data('parent') || 0 // 父级物料ID
            };
            materials.push(materialData);
        });

        // 添加物料数据到表单
        data.field.materials = materials;

        var callback = function(res) {
            layer.msg(res.msg);
            if (res.code == 0) {
                setTimeout(function() {
                    parent.layui.pageTable.reload();
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }, 1000);
            }
        };

        tool.post("/material/bom/edit", data.field, callback);
        return false;
    });
}
</script>
{/block}
<!-- /脚本 -->
