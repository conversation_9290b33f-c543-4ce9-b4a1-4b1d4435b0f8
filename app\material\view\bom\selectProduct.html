{extend name="../../base/view/common/base" /}
{block name="style"}
<div class="layui-fluid">
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>选择产品</h3>
                </div>
                <div class="layui-card-body">
                    <!-- 搜索表单 -->
                    <form class="layui-form" lay-filter="searchForm">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">产品分类</label>
                                    <div class="layui-input-block">
                                        <select name="cate_id" lay-search="">
                                            <option value="">全部分类</option>
                                            {volist name="categories" id="cate"}
                                            <option value="{$cate.id}">{$cate.title}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">关键词</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="keyword" placeholder="产品名称或编码" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button type="submit" class="layui-btn" lay-submit lay-filter="search">
                                            <i class="layui-icon layui-icon-search"></i> 搜索
                                        </button>
                                        <button type="reset" class="layui-btn layui-btn-primary">
                                            <i class="layui-icon layui-icon-refresh"></i> 重置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 产品列表表格 -->
                    <table class="layui-hide" id="productTable" lay-filter="productTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表格工具栏 -->
<script type="text/html" id="tableToolbar">
    <div class="layui-btn-container">
        <span style="color: #666;">请选择要添加到BOM的产品</span>
    </div>
</script>

<!-- 操作列模板 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="select">选择</a>
</script>
{/block}

{block name="script"}
<script>
const moduleInit = ['table', 'form'];
function gouguInit() {
    var table = layui.table, form = layui.form;

    // 渲染表格
    table.render({
        elem: '#productTable',
        url: '/material/bom/selectProduct',
        toolbar: '#tableToolbar',
        defaultToolbar: [],
        cols: [[
            {field: 'id', width: 80, title: 'ID', align: 'center'},
            {field: 'material_code', width: 150, title: '产品编码', align: 'center'},
            {field: 'title', width: 200, title: '产品名称'},
            {field: 'specs', width: 150, title: '规格型号'},
            {field: 'unit', width: 80, title: '单位', align: 'center'},
            {width: 100, title: '操作', templet: '#tableBar', fixed: 'right', align: 'center'}
        ]],
        page: true,
        limit: 10,
        text: {
            none: '暂无产品数据'
        }
    });

    // 监听搜索
    form.on('submit(search)', function(data) {
        table.reload('productTable', {
            where: data.field,
            page: {
                curr: 1
            }
        });
        return false;
    });

    // 监听表格工具条
    table.on('tool(productTable)', function(obj) {
        var data = obj.data;
        if (obj.event === 'select') {
            // 选择产品
            selectProductCallback(data);
        }
    });

    // 产品选择回调函数
    window.selectProductCallback = function(productData) {
        // 获取父窗口
        var parentWindow = parent.window;
        
        // 填充产品信息到父窗口的表单
        if (parentWindow.$) {
            parentWindow.$('input[name="product_name"]').val(productData.title);
            parentWindow.$('input[name="product_code"]').val(productData.material_code);
            parentWindow.$('input[name="product_id"]').val(productData.id);

            // 如果有规格字段，也填充
            if (parentWindow.$('input[name="specifications"]').length > 0) {
                parentWindow.$('input[name="specifications"]').val(productData.specs || '');
            }

            // 触发父窗口的表单验证
            if (parentWindow.layui && parentWindow.layui.form) {
                parentWindow.layui.form.render();
            }
        }
        
        // 关闭当前弹窗
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        
        // 提示选择成功
        parent.layer.msg('产品选择成功，现在可以选择物料了', {icon: 1, time: 3000});
    };
}
</script>
{/block}
