{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.p-4 {
    padding: 15px;
}
.p-page {
    padding: 15px;
}
.layui-td-gray {
    background-color: #f8f8f8;
    font-weight: bold;
}
.info-value {
    padding: 8px 12px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    min-height: 38px;
    line-height: 22px;
}
</style>
{/block}

{block name="body"}
<div class="layui-tab layui-tab-brief" lay-filter="materialViewTab">
    <ul class="layui-tab-title">
        <li class="layui-this">基础资料</li>
        <li>质检信息</li>
        <li>价格信息</li>
        <li>工艺管理</li>
    </ul>
    <div class="layui-tab-content">
        <!-- 基础资料 -->
        <div class="layui-tab-item layui-show">
            <div class="p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">物料编号</td>
                        <td><div class="info-value">{$detail.material_code}</div></td>
                        <td class="layui-td-gray">物料名称</td>
                        <td><div class="info-value">{$detail.title}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料分类</td>
                        <td><div class="info-value">{$detail.category_name|default='未分类'}</div></td>
                        <td class="layui-td-gray">基本单位</td>
                        <td><div class="info-value">{$detail.unit|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">规格型号</td>
                        <td><div class="info-value">{$detail.specs|default='-'}</div></td>
                        <td class="layui-td-gray">物料类型</td>
                        <td><div class="info-value">{if condition="$detail.source_type == 1"}外购{elseif condition="$detail.source_type == 2"}自制{else/}-{/if}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">状态</td>
                        <td><div class="info-value">{if condition="$detail.status == 1"}启用{else/}禁用{/if}</div></td>
                        <td class="layui-td-gray">生产厂家</td>
                        <td><div class="info-value">{$detail.producer|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">采购周期(天)</td>
                        <td><div class="info-value">{$detail.purchase_cycle|default='-'}</div></td>
                        <td class="layui-td-gray">库存数量</td>
                        <td><div class="info-value">{$detail.stock|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">默认仓库</td>
                        <td><div class="info-value">{$detail.default_warehouse|default='-'}</div></td>
                        <td class="layui-td-gray">最小订购量</td>
                        <td><div class="info-value">{$detail.min_order_qty|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">最小包装量</td>
                        <td><div class="info-value">{$detail.min_package_qty|default='-'}</div></td>
                        <td class="layui-td-gray">物料等级</td>
                        <td><div class="info-value">{$detail.material_level|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料来源</td>
                        <td><div class="info-value">{$detail.material_source|default='-'}</div></td>
                        <td class="layui-td-gray">类别</td>
                        <td><div class="info-value">{$detail.category|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">型号</td>
                        <td><div class="info-value">{$detail.model|default='-'}</div></td>
                        <td class="layui-td-gray">颜色</td>
                        <td><div class="info-value">{$detail.color|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料描述</td>
                        <td colspan="3"><div class="info-value">{$detail.description|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">备注信息</td>
                        <td colspan="3"><div class="info-value">{$detail.remark|default='-'}</div></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 质检信息 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">质量管理</td>
                        <td><div class="info-value">{if condition="$detail.quality_management == 1"}是{else/}否{/if}</div></td>
                        <td class="layui-td-gray">质检免检</td>
                        <td><div class="info-value">{if condition="$detail.quality_exempt == 1"}是{else/}否{/if}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">检验项目</td>
                        <td colspan="3">
                            <div class="info-value">
                                {if condition="$detail.quality_management == 1 && $detail.quality_exempt == 0"}
                                    {if condition="!empty($detail.quality_settings)"}
                                        {php}
                                            $quality_settings = json_decode($detail['quality_settings'], true);
                                            if (is_array($quality_settings) && !empty($quality_settings)) {
                                                $quality_map = [
                                                    'purchase_inspect' => '采购入库检',
                                                    'outsource_inspect' => '外协入库检',
                                                    'production_inspect' => '生产入库检',
                                                    'sales_inspect' => '销售出库检'
                                                ];
                                                $quality_names = [];
                                                foreach ($quality_settings as $setting) {
                                                    if (isset($quality_map[$setting])) {
                                                        $quality_names[] = $quality_map[$setting];
                                                    }
                                                }
                                                echo !empty($quality_names) ? implode('、', $quality_names) : '无';
                                            } else {
                                                echo '无';
                                            }
                                        {/php}
                                    {else/}
                                        无
                                    {/if}
                                {elseif condition="$detail.quality_exempt == 1"/}
                                    免检
                                {else/}
                                    无
                                {/if}
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
        </div>
        
        <!-- 价格信息 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">参考成本</td>
                        <td><div class="info-value">{$detail.reference_cost|default='-'}</div></td>
                        <td class="layui-td-gray">销售价格</td>
                        <td><div class="info-value">{$detail.sales_price|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">最低销售价</td>
                        <td><div class="info-value">{$detail.min_sales_price|default='-'}</div></td>
                        <td class="layui-td-gray">最高销售价</td>
                        <td><div class="info-value">{$detail.max_sales_price|default='-'}</div></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 工艺管理 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <div class="layui-tab layui-tab-brief" lay-filter="processTab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">供应商价格</li>
                        <li>外协价格</li>
                        <li>附件管理</li>
                    </ul>
                    <div class="layui-tab-content">
                        <!-- 供应商价格 -->
                        <div class="layui-tab-item layui-show">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>供应商</th>
                                        <th>优先级</th>
                                        <th>最小数量</th>
                                        <th>最大数量</th>
                                        <th>含税价格</th>
                                        <th>税率(%)</th>
                                        <th>不含税价格</th>
                                    </tr>
                                </thead>
                                <tbody id="supplierPriceList">
                                    {if condition="!empty($supplierPrices)"}
                                        {volist name="supplierPrices" id="price"}
                                        <tr>
                                            <td>{$price.supplier_name|default='-'}</td>
                                            <td>{$price.priority|default='-'}</td>
                                            <td>{$price.min_qty|default='-'}</td>
                                            <td>{$price.max_qty|default='-'}</td>
                                            <td>{$price.tax_price|default='-'}</td>
                                            <td>{$price.tax_rate|default='-'}</td>
                                            <td>{$price.no_tax_price|default='-'}</td>
                                        </tr>
                                        {/volist}
                                    {else/}
                                        <tr><td colspan="7" style="text-align:center;">暂无数据</td></tr>
                                    {/if}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 外协价格 -->
                        <div class="layui-tab-item">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>外协商</th>
                                        <th>优先级</th>
                                        <th>最小数量</th>
                                        <th>最大数量</th>
                                        <th>含税价格</th>
                                        <th>税率(%)</th>
                                        <th>不含税价格</th>
                                    </tr>
                                </thead>
                                <tbody id="outsourcePriceList">
                                    {if condition="!empty($outsourcePrices)"}
                                        {volist name="outsourcePrices" id="price"}
                                        <tr>
                                            <td>{$price.supplier_name|default='-'}</td>
                                            <td>{$price.priority|default='-'}</td>
                                            <td>{$price.min_qty|default='-'}</td>
                                            <td>{$price.max_qty|default='-'}</td>
                                            <td>{$price.tax_price|default='-'}</td>
                                            <td>{$price.tax_rate|default='-'}</td>
                                            <td>{$price.no_tax_price|default='-'}</td>
                                        </tr>
                                        {/volist}
                                    {else/}
                                        <tr><td colspan="7" style="text-align:center;">暂无数据</td></tr>
                                    {/if}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 附件管理 -->
                        <div class="layui-tab-item">
                            <div class="info-value">
                                {if condition="!empty($detail.attachments)"}
                                    {php}
                                        $attachments = json_decode($detail['attachments'], true);
                                        if (is_array($attachments) && !empty($attachments)) {
                                            foreach ($attachments as $attachment) {
                                                echo '<p><a href="' . $attachment['url'] . '" target="_blank">' . $attachment['name'] . '</a></p>';
                                            }
                                        } else {
                                            echo '暂无附件';
                                        }
                                    {/php}
                                {else/}
                                    暂无附件
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    layui.use(['element'], function () {
        var element = layui.element;

        // 监听tab切换
        element.on('tab(materialViewTab)', function(data){
            console.log('切换到：', data.index);
        });

        // 监听工艺管理内部tab切换
        element.on('tab(processTab)', function(data){
            console.log('工艺管理切换到：', data.index);
        });

        // 供应商价格和外协价格数据已在服务端加载
    });
}


</script>
{/block}
