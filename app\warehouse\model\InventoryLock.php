<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;
use think\facade\Db;

/**
 * 库存锁定模型
 */
class InventoryLock extends Model
{
    // 设置表名
    protected $name = 'inventory_lock';
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'inventory_id'      => 'int',
        'product_id'        => 'int',
        'warehouse_id'      => 'int',
        'location_id'       => 'int',
        'batch_no'          => 'string',
        'quantity'          => 'float',
        'unit'              => 'string',
        'cost_price'        => 'float',
        'ref_type'          => 'string',
        'ref_id'            => 'int',
        'ref_no'            => 'string',
        'lock_time'         => 'int',
        'expire_time'       => 'int',
        'auto_release'      => 'int',
        'release_condition' => 'string',
        'status'            => 'int',
        'created_by'        => 'int',
        'create_time'       => 'int',
        'update_time'       => 'int',
        'remark'            => 'string'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 状态常量
    const STATUS_RELEASED = 0;    // 已释放
    const STATUS_LOCKED = 1;      // 已锁定
    const STATUS_USED = 2;        // 已使用
    const STATUS_EXPIRED = 3;     // 已过期
    
    // 锁定类型常量
    const TYPE_ORDER = 'customer_order';        // 客户订单锁定
    const TYPE_PRODUCTION = 'production_order'; // 生产订单锁定
    const TYPE_TRANSFER = 'transfer_order';     // 调拨单锁定
    const TYPE_SAMPLE = 'sample_order';         // 样品单锁定
    const TYPE_MANUAL = 'manual_lock';          // 手动锁定
    const TYPE_QUALITY = 'quality_check';       // 质检锁定
    
    /**
     * 关联库存模型
     */
    public function inventory()
    {
        return $this->belongsTo(Inventory::class, 'inventory_id');
    }
    
    /**
     * 关联产品模型
     */
    public function product()
    {
        return $this->belongsTo('app\product\model\Product', 'product_id');
    }
    
    /**
     * 关联仓库模型
     */
    public function warehouse()
    {
        return $this->belongsTo('app\warehouse\model\Warehouse', 'warehouse_id');
    }
    
    /**
     * 关联库位模型
     */
    public function location()
    {
        return $this->belongsTo('app\warehouse\model\Location', 'location_id');
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo('app\admin\model\Admin', 'created_by');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_RELEASED => '已释放',
            self::STATUS_LOCKED => '已锁定',
            self::STATUS_USED => '已使用',
            self::STATUS_EXPIRED => '已过期'
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }
    
    /**
     * 获取锁定类型文本
     */
    public function getRefTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::TYPE_ORDER => '客户订单',
            self::TYPE_PRODUCTION => '生产订单',
            self::TYPE_TRANSFER => '调拨单',
            self::TYPE_SAMPLE => '样品单',
            self::TYPE_MANUAL => '手动锁定',
            self::TYPE_QUALITY => '质检锁定'
        ];
        
        return $typeMap[$data['ref_type']] ?? $data['ref_type'];
    }
    
    /**
     * 获取格式化的锁定时间
     */
    public function getLockTimeTextAttr($value, $data)
    {
        return $data['lock_time'] ? date('Y-m-d H:i:s', $data['lock_time']) : '';
    }
    
    /**
     * 获取格式化的过期时间
     */
    public function getExpireTimeTextAttr($value, $data)
    {
        return $data['expire_time'] ? date('Y-m-d H:i:s', $data['expire_time']) : '';
    }
    
    /**
     * 检查是否已过期
     */
    public function getIsExpiredAttr($value, $data)
    {
        if ($data['expire_time'] <= 0) {
            return false;
        }
        
        return $data['expire_time'] < time();
    }
    
    /**
     * 搜索器：产品ID
     */
    public function searchProductIdAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('product_id', '=', $value);
        }
    }
    
    /**
     * 搜索器：仓库ID
     */
    public function searchWarehouseIdAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('warehouse_id', '=', $value);
        }
    }
    
    /**
     * 搜索器：状态
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('status', '=', $value);
        }
    }
    
    /**
     * 搜索器：锁定类型
     */
    public function searchRefTypeAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('ref_type', '=', $value);
        }
    }
    
    /**
     * 搜索器：关联单据编号
     */
    public function searchRefNoAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('ref_no', 'like', '%' . $value . '%');
        }
    }
    
    /**
     * 搜索器：批次号
     */
    public function searchBatchNoAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('batch_no', 'like', '%' . $value . '%');
        }
    }
    
    /**
     * 搜索器：时间范围
     */
    public function searchTimeRangeAttr($query, $value, $data)
    {
        if (!empty($value) && is_array($value) && count($value) == 2) {
            $startTime = strtotime($value[0]);
            $endTime = strtotime($value[1] . ' 23:59:59');
            $query->whereBetween('lock_time', [$startTime, $endTime]);
        }
    }
    
    /**
     * 获取产品的锁定数量
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID，0表示所有仓库
     * @return float
     */
    public static function getProductLockedQuantity($productId, $warehouseId = 0)
    {
        $where = [
            'product_id' => $productId,
            'status' => self::STATUS_LOCKED
        ];
        
        if ($warehouseId > 0) {
            $where['warehouse_id'] = $warehouseId;
        }
        
        return self::where($where)->sum('quantity') ?: 0;
    }
    
    /**
     * 获取即将过期的锁定记录
     * @param int $hours 多少小时内过期
     * @return array
     */
    public static function getExpiringLocks($hours = 24)
    {
        $expireTime = time() + ($hours * 3600);
        
        return self::where([
            'status' => self::STATUS_LOCKED,
            'expire_time' => ['>', 0],
            'expire_time' => ['<=', $expireTime]
        ])
        ->with(['product', 'warehouse', 'location', 'creator'])
        ->select()
        ->toArray();
    }
    
    /**
     * 批量释放锁定
     * @param array $lockIds 锁定记录ID数组
     * @param int $operatorId 操作人ID
     * @param string $reason 释放原因
     * @return array
     */
    public static function batchRelease($lockIds, $operatorId, $reason = '')
    {
        if (empty($lockIds)) {
            return ['code' => 1, 'msg' => '锁定记录ID不能为空'];
        }
        
        Db::startTrans();
        try {
            $count = 0;
            foreach ($lockIds as $lockId) {
                $lock = self::find($lockId);
                if ($lock && $lock->status == self::STATUS_LOCKED) {
                    $lock->status = self::STATUS_RELEASED;
                    $lock->update_time = time();
                    if ($lock->save()) {
                        $count++;
                        
                        // 记录释放日志
                        Db::name('inventory_lock_log')->insert([
                            'lock_id' => $lockId,
                            'product_id' => $lock->product_id,
                            'warehouse_id' => $lock->warehouse_id,
                            'operation_type' => 'release',
                            'quantity' => $lock->quantity,
                            'before_quantity' => $lock->quantity,
                            'after_quantity' => 0,
                            'ref_type' => $lock->ref_type,
                            'ref_id' => $lock->ref_id,
                            'ref_no' => $lock->ref_no,
                            'notes' => $reason ?: '批量释放锁定',
                            'created_by' => $operatorId,
                            'create_time' => time()
                        ]);
                    }
                }
            }
            
            Db::commit();
            return ['code' => 0, 'msg' => "成功释放 {$count} 条锁定记录"];
            
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 1, 'msg' => '释放失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 统计锁定数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getStatistics($where = [])
    {
        $query = self::where($where);
        
        return [
            'total_count' => $query->count(),
            'locked_count' => $query->where('status', self::STATUS_LOCKED)->count(),
            'used_count' => $query->where('status', self::STATUS_USED)->count(),
            'released_count' => $query->where('status', self::STATUS_RELEASED)->count(),
            'expired_count' => $query->where('status', self::STATUS_EXPIRED)->count(),
            'total_quantity' => $query->sum('quantity'),
            'locked_quantity' => $query->where('status', self::STATUS_LOCKED)->sum('quantity')
        ];
    }
}