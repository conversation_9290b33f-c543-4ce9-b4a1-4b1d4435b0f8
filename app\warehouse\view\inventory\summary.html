{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
    <div class="gg-form-bar border-t border-x" style="padding-bottom:10px;">
        <div class="layui-form layui-form-pane">
            <div class="layui-form-item layui-row layui-col-space10">
                <div class="layui-col-md3">
                    <select name="warehouse_id">
                        <option value="">所有仓库</option>
                        {volist name="warehouses" id="warehouse"}
                        <option value="{$warehouse.id}" {if $warehouse_id==$warehouse.id}selected{/if}>{$warehouse.name}</option>
                        {/volist}
                    </select>
                </div>
                <div class="layui-col-md3">
                    <input type="text" name="keyword" placeholder="产品名称/编码" value="{$keyword}" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <button type="submit" class="layui-btn" lay-submit>查询</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="exportBtn">导出</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title">
            <li><a href="{:url('warehouse/inventory/index')}">库存列表</a></li>
            <li class="layui-this"><a href="{:url('warehouse/inventory/summary')}">库存汇总</a></li>
            <li><a href="{:url('warehouse/inventory/logs')}">库存日志</a></li>
            <li><a href="{:url('warehouse/inventory/alerts')}">库存预警</a></li>
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <table class="layui-table" id="summary-table" lay-filter="summary-table"></table>
            </div>
        </div>
    </div>
</div>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var table = layui.table;
    var layer = layui.layer;
    var $ = layui.$;
    
    // 初始化表格
    table.render({
        elem: '#summary-table',
        url: '{:url("warehouse/inventory/summary")}',
        page: true,
        cols: [[
            {field: 'product_code', title: '产品编码', width: 120},
            {field: 'product_name', title: '产品名称', width: 180},
            {field: 'product_spec', title: '产品规格', width: 120},
            {field: 'total_quantity', title: '总库存数量', width: 120, sort: true},
            {field: 'total_available', title: '总可用数量', width: 120, sort: true},
            {field: 'total_locked', title: '总锁定数量', width: 130, sort: true},
            {field: 'avg_cost_price', title: '平均成本价', width: 120, sort: true, templet: function(d) {
                return '￥' + parseFloat(d.avg_cost_price || 0).toFixed(2);
            }},
            {field: 'unit', title: '单位', width: 80},
            {title: '操作', width: 120, align: 'center', templet: function(d) {
                return '<a href="{:url("warehouse/inventory/distribution")}?product_id=' + d.product_id + '" class="layui-btn layui-btn-xs">分布详情</a>';
            }}
        ]]
    });
    
    // 导出按钮点击事件
    $('#exportBtn').on('click', function(){
        var params = new URLSearchParams(window.location.search);
        params.set('export', 1);
        var url = '{:url("warehouse/inventory/summary")}?' + params.toString();
        window.open(url);
    });
}
</script>
{/block} 