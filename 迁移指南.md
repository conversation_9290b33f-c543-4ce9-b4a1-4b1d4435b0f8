# 从预占机制到锁定机制的迁移指南

## 概述

本指南将帮助您从原有的库存预占机制平滑迁移到新的库存锁定机制。新机制更加简洁高效，取消了预占步骤，在业务审核后直接锁定库存。

## 迁移前准备

### 1. 环境检查
- 确保数据库备份完整
- 确认当前系统中预占数据的状态
- 检查是否有正在进行的业务流程

### 2. 影响评估
- 统计当前有效的预占记录数量
- 确认哪些业务模块使用了预占功能
- 评估迁移对现有业务的影响

## 详细迁移步骤

### 第一步：数据备份

```bash
# 1. 备份整个数据库
mysqldump -u用户名 -p 数据库名 > full_backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 备份相关表
mysqldump -u用户名 -p 数据库名 oa_inventory_reserve oa_inventory_reserve_log oa_inventory oa_inventory_lock > inventory_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 第二步：处理现有预占数据

在删除预占表之前，需要处理现有的预占数据：

```sql
-- 查看当前有效预占记录
SELECT 
    ir.id,
    ir.product_id,
    p.title as product_name,
    ir.quantity,
    ir.ref_type,
    ir.ref_no,
    ir.status,
    FROM_UNIXTIME(ir.create_time) as create_time
FROM oa_inventory_reserve ir
LEFT JOIN oa_product p ON ir.product_id = p.id
WHERE ir.status = 1
ORDER BY ir.create_time DESC;

-- 可选：将有效预占转换为锁定记录
-- 这需要根据具体业务逻辑来决定
```

### 第三步：执行数据库迁移

#### 方式一：使用简化版脚本（推荐）

```bash
mysql -u用户名 -p 数据库名 < delete_inventory_reserve_simple.sql
```

#### 方式二：手动执行关键SQL

```sql
-- 删除预占表
DROP TABLE IF EXISTS `oa_inventory_reserve`;
DROP TABLE IF EXISTS `oa_inventory_reserve_log`;

-- 创建锁定表（如果不存在）
-- 参考 delete_inventory_reserve_simple.sql 中的完整建表语句
```

### 第四步：验证迁移结果

```bash
# 运行检查脚本
php check_database_migration.php
```

检查脚本会验证：
- 预占表是否已删除
- 锁定表是否创建成功
- 必要的字段和索引是否存在
- 库存表中的预占字段是否需要清理

### 第五步：代码更新

#### 5.1 删除预占相关文件
- `app/warehouse/service/InventoryReservationService.php` ✅ 已删除
- `app/warehouse/model/Inventoryreserve.php` ✅ 已删除

#### 5.2 更新业务代码

**客户订单模块示例：**

```php
// 旧代码（预占机制）
$reservationService = new InventoryReservationService();
$result = $reservationService->createReservation($productId, $quantity, 'customer_order', $orderId, $orderNo);

// 新代码（锁定机制）
$lockService = new InventoryLockService();
$result = $lockService->lockInventory([
    'product_id' => $productId,
    'quantity' => $quantity,
    'warehouse_id' => $warehouseId,
    'ref_type' => 'customer_order',
    'ref_id' => $orderId,
    'ref_no' => $orderNo,
    'created_by' => session('admin.id')
]);
```

#### 5.3 更新视图文件
- ✅ 已更新 `app/warehouse/view/inventory/summary.html`
- ✅ 已更新 `app/warehouse/view/inventory/lockList.html`

#### 5.4 更新控制器
- ✅ 已更新 `app/warehouse/controller/Inventory.php`

### 第六步：业务流程调整

#### 原有流程：
1. 订单创建 → 预占库存
2. 订单审核 → 保持预占
3. 订单出库 → 使用预占，减少库存

#### 新流程：
1. 订单创建 → 不锁定库存
2. 订单审核 → 直接锁定库存
3. 订单出库 → 使用锁定，减少库存

### 第七步：测试验证

#### 7.1 功能测试
- [ ] 库存锁定功能
- [ ] 库存释放功能  
- [ ] 批量锁定功能
- [ ] 过期锁定处理
- [ ] 库存状态查询

#### 7.2 业务场景测试
- [ ] 客户订单审核锁定
- [ ] 客户订单出库使用锁定
- [ ] 客户订单取消释放锁定
- [ ] 生产订单原料锁定
- [ ] 调拨单锁定

#### 7.3 数据一致性测试
- [ ] 库存数量准确性
- [ ] 锁定数量统计
- [ ] 可用库存计算
- [ ] 日志记录完整性

### 第八步：上线部署

#### 8.1 部署前检查
- [ ] 代码审查完成
- [ ] 测试用例全部通过
- [ ] 数据库迁移脚本验证
- [ ] 回滚方案准备

#### 8.2 部署步骤
1. 停止相关业务操作
2. 执行数据库迁移
3. 部署新代码
4. 验证核心功能
5. 恢复业务操作

#### 8.3 部署后监控
- 监控库存数据变化
- 检查错误日志
- 验证业务流程正常
- 收集用户反馈

## 回滚方案

如果迁移过程中出现问题，可以按以下步骤回滚：

### 1. 数据库回滚
```bash
# 恢复数据库备份
mysql -u用户名 -p 数据库名 < full_backup_YYYYMMDD_HHMMSS.sql
```

### 2. 代码回滚
- 恢复删除的预占相关文件
- 回滚控制器和视图的修改
- 恢复原有的业务逻辑

## 常见问题处理

### Q1: 迁移后库存数量不准确怎么办？
A1: 
1. 检查迁移前的数据备份
2. 对比迁移前后的库存总量
3. 重新计算库存可用量
4. 必要时手动调整数据

### Q2: 锁定功能不生效怎么办？
A2:
1. 检查 `oa_inventory_lock` 表是否创建成功
2. 验证 `InventoryLockService` 类是否正确加载
3. 检查数据库连接和权限
4. 查看错误日志定位问题

### Q3: 业务流程中断怎么办？
A3:
1. 立即执行回滚方案
2. 分析中断原因
3. 修复问题后重新迁移
4. 通知相关业务人员

## 迁移检查清单

### 迁移前
- [ ] 数据库完整备份
- [ ] 预占数据状态确认
- [ ] 业务影响评估
- [ ] 迁移方案审查

### 迁移中
- [ ] 预占表删除成功
- [ ] 锁定表创建成功
- [ ] 索引创建完成
- [ ] 代码更新部署

### 迁移后
- [ ] 功能测试通过
- [ ] 数据一致性验证
- [ ] 业务流程正常
- [ ] 性能指标正常

## 联系支持

如果在迁移过程中遇到问题，请：
1. 查看本指南的常见问题部分
2. 检查系统错误日志
3. 联系技术支持团队
4. 准备详细的问题描述和错误信息

---

**重要提醒：** 
- 迁移前务必做好完整备份
- 建议在测试环境先完整验证
- 迁移过程中保持与业务团队的沟通
- 准备好回滚方案以应对突发情况