<?php
declare (strict_types = 1);

namespace app\warehouse\controller;
use app\base\BaseController;
use app\warehouse\model\Inventory as InventoryModel;
use app\warehouse\model\Warehouse as WarehouseModel;
use app\warehouse\model\Location as LocationModel;
use app\warehouse\model\Zone as ZoneModel;
use app\product\model\Product as ProductModel;
use think\facade\View;
use think\facade\Db;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use app\warehouse\service\InventoryLockService;

/**
 * 库存查询控制器
 */
class Inventory extends BaseController
{
    /**
     * 库存查询列表
     */
    public function index()
    {
        // 获取查询参数
        $productId = input('product_id', 0, 'intval');
        $warehouseId = input('warehouse_id', 0, 'intval');
        $zoneId = input('zone_id', 0, 'intval');
        $locationId = input('location_id', 0, 'intval');
        $batchNo = input('batch_no', '', 'trim');
        $status = input('status', '', 'trim');
        $keyword = input('keyword', '', 'trim');
        $export = input('export', 0, 'intval');
        
        // 构建查询条件
        $where = [];
        
        if ($productId > 0) {
            $where[] = ['i.product_id', '=', $productId];
        }
        
        if ($warehouseId > 0) {
            $where[] = ['i.warehouse_id', '=', $warehouseId];
        }
        
        if ($locationId > 0) {
            $where[] = ['i.location_id', '=', $locationId];
        }
        
        if (!empty($batchNo)) {
            $where[] = ['i.batch_no', 'like', '%' . $batchNo . '%'];
        }
        
        if ($status !== '') {
            $where[] = ['i.status', '=', intval($status)];
        }
        
        if (!empty($keyword)) {
            // 在产品名称、编码和批次号中搜索关键词
            $product_ids = ProductModel::where('name|code', 'like', '%' . $keyword . '%')->column('id');
            if (!empty($product_ids)) {
                $where[] = ['i.product_id', 'in', $product_ids];
            } else {
                $where[] = ['i.batch_no', 'like', '%' . $keyword . '%'];
            }
        }
       
        // 导出功能
        if ($export) {
            // 实现导出功能
            return $this->exportInventory($where);
        }
       
        // 联表查询，获取库存列表
        $list = InventoryModel::alias('i')
            ->join('product p', 'i.product_id = p.id')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->leftJoin('warehouse_location l', 'i.location_id = l.id')
            ->field('i.*, p.title as product_name, p.material_code as product_code, p.specs as product_spec, 
                w.name as warehouse_name, l.name as location_name, p.id as product_id,
                p.unit as unit')
            ->where($where)
            ->order('i.id', 'desc')
            ->paginate([
                'list_rows' => input('limit', 15),
                'page' => input('page', 1),
            ]);
        
        // 处理库存项数据
        $items = $list->items();
        foreach ($items as &$item) {
            // 设置可用库存数量等于实际库存（因为没有预占和锁库）
            $item['available_quantity'] = $item['quantity'];
            
            // 如果unit字段为空，尝试从产品表获取
            if (empty($item['unit'])) {
                $item['unit'] = ProductModel::where('id', $item['product_id'])->value('unit') ?: '';
            }
        }
        
        // 检查是否为AJAX请求或带有返回JSON的参数
        if (request()->isAjax()) {
            // 处理分页数据为layui表格所需格式
            $result = [
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $items
            ];
            return json($result);
        }
          
        // 获取仓库列表
        $warehouses = WarehouseModel::where('status', 1)->select();
        
        // 若有选择仓库，获取该仓库下的区域
        $zones = [];
        /*
        if ($warehouseId > 0) {
            $zones = ZoneModel::where('warehouse_id', $warehouseId)
                ->where('status', 1)
                ->select();
        }
        */
        
        // 若有选择区域，获取该区域下的库位
        $locations = [];
        if ($warehouseId > 0) {
            $locationWhere = ['warehouse_id' => $warehouseId, 'status' => 1];
            if ($zoneId > 0) {
                $locationWhere['zone_id'] = $zoneId;
            }
            $locations = LocationModel::where($locationWhere)->select();
        }
        
        // 状态列表
        $statusList = [
            ['value' => '1', 'label' => '正常'],
            ['value' => '2', 'label' => '冻结'],
            ['value' => '3', 'label' => '待检']
        ];
        
        // 模板赋值
        View::assign([
            'list' => $list,
            'warehouses' => $warehouses,
            'zones' => $zones,
            'locations' => $locations,
            'status_list' => $statusList,
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'zone_id' => $zoneId,
            'location_id' => $locationId,
            'batch_no' => $batchNo,
            'status' => $status,
            'keyword' => $keyword
        ]);
        
        // 返回视图
        return View::fetch();
    }
    
    /**
     * 库存汇总查询
     */
    public function summary()
    {
        // 获取查询参数
        $productId = input('product_id', 0, 'intval');
        $warehouseId = input('warehouse_id', 0, 'intval');
        $keyword = input('keyword', '', 'trim');
        $export = input('export', 0, 'intval');
        
        // 构建基础查询SQL
        $baseQuery = "SELECT p.id as product_id, p.title as product_name, p.material_code as product_code, 
                p.specs as product_spec, p.unit, 
                SUM(i.quantity) as total_quantity, 
                SUM(i.available_quantity) as total_available, 
                AVG(i.cost_price) as avg_cost_price
                FROM oa_inventory i
                JOIN oa_product p ON i.product_id = p.id";
                
        
        // 构建WHERE条件 total_allocated
        $whereClause = " WHERE 1=1";
        $params = [];
        
        if ($productId > 0) {
            $whereClause .= " AND i.product_id = :product_id";
            $params['product_id'] = $productId;
        }
        
        if ($warehouseId > 0) {
            $whereClause .= " AND i.warehouse_id = :warehouse_id";
            $params['warehouse_id'] = $warehouseId;
        }
        
        if (!empty($keyword)) {
            $whereClause .= " AND (p.title LIKE :keyword OR p.code LIKE :keyword)";
            $params['keyword'] = "%{$keyword}%";
        }
        
        // 分组和排序
        $groupBy = " GROUP BY p.id, p.title, p.material_code, p.specs, p.unit";
        $orderBy = " ORDER BY p.material_code ASC";
        
        // 获取总记录数
        $countQuery = "SELECT COUNT(*) AS total FROM (SELECT p.id FROM oa_inventory i JOIN oa_product p ON i.product_id = p.id {$whereClause} GROUP BY p.id) as tmp";
        $total = Db::query($countQuery, $params)[0]['total'];
        
        // 如果是导出，则使用模型方法生成报表
        if ($export) {
            return $this->exportSummary($warehouseId, $productId, $keyword);
        }
        
        // 分页
        $page = input('page', 1, 'intval');
        $limit = input('limit', 15, 'intval');
        $offset = ($page - 1) * $limit;
        
        // 分页查询
        $pagingQuery = "{$baseQuery} {$whereClause} {$groupBy} {$orderBy} LIMIT {$offset}, {$limit}";
        $list = Db::query($pagingQuery, $params);

        foreach ($list as &$item) {
           // print_r($item);
            // 获取锁定数量
            $lockedQuantity = Db::name('inventory_lock')
                ->where('product_id', $item['product_id'])
                ->where('status', 1) // 已锁定状态
                ->sum('quantity');
            
            $item['total_locked'] = $lockedQuantity ?: 0;
            $item['total_available'] = $item['total_quantity'] - $item['total_locked'];
        }
        
        // 加载仓库列表
        $warehouses = WarehouseModel::where('status', 1)->select();

        // 检查是否为AJAX请求
        if (request()->isAjax()) {
            // 处理分页数据为layui表格所需格式
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $total,
                'data' => $list
            ]);
        }
        
        // 模板赋值
        View::assign([
            'list' => $list,
            'warehouses' => $warehouses,
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'keyword' => $keyword,
            'page' => $page,
            'limit' => $limit,
            'total' => $total
        ]);
        
        // 返回视图
        return View::fetch();
    }
    
    /**
     * 库存分布查询
     */
    public function distribution()
    {
        // 获取产品ID
        $productId = input('product_id', 0, 'intval');
        $isAjax = input('ajax', 0, 'intval');
        
        if (empty($productId)) {
            $this->error('请选择要查询的产品');
        }
        
        // 获取产品信息
        $product = ProductModel::find($productId);
        if (empty($product)) {
            $this->error('产品不存在');
        }
        
        // 查询产品在各仓库的库存分布
        $query = InventoryModel::alias('i')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->leftJoin('warehouse_location l', 'i.location_id = l.id')
            ->leftJoin('warehouse_zone z', 'l.zone_id = z.id')
            ->field('i.*, w.name as warehouse_name, l.name as location_name, 
                z.name as zone_name')
            ->where('i.product_id', $productId);
        
        // 计算总库存数量
        $total_quantity = InventoryModel::where('product_id', $productId)->sum('quantity');
        
        // AJAX请求返回JSON数据
        if ($isAjax || request()->isAjax()) {
            $list = $query->order('i.id', 'desc')
                ->select()
                ->toArray();
                
            return json([
                'code' => 0,
                'msg' => '',
                'count' => count($list),
                'data' => $list
            ]);
        }
        
        // 查询分布列表
        $distribution = $query->select();
        
        // 模板赋值
        View::assign([
            'product' => $product,
            'distribution' => $distribution,
            'total_quantity' => $total_quantity
        ]);
        
        // 返回视图
        return View::fetch();
    }
    
    /**
     * 获取指定仓库下的区域列表（AJAX）
     */
    public function getZones()
    {
        $warehouseId = input('warehouse_id', 0, 'intval');
        
        // 返回空数组，因为warehouse_zone表不存在
        return json(['code' => 0, 'data' => [], 'msg' => '暂无库区数据']);
    }
    
    /**
     * 获取指定仓库和区域下的库位列表（AJAX）
     */
    public function getLocations()
    {
        $warehouseId = input('warehouse_id', 0, 'intval');
        $zoneId = input('zone_id', 0, 'intval');
        
        $where = ['warehouse_id' => $warehouseId, 'status' => 1];
        if ($zoneId > 0) {
            $where['zone_id'] = $zoneId;
        }
        
        $locations = LocationModel::where($where)
            ->field('id, code, name')
            ->select()
            ->toArray();
        
        return json(['code' => 0, 'data' => $locations]);
    }
    
    /**
     * 库存日志查询
     */
    public function logs()
    {
        // 获取查询参数
        $productId = input('product_id', 0, 'intval');
        $warehouseId = input('warehouse_id', 0, 'intval');
        $type = input('type', '', 'trim');
        $startTime = input('start_time', '', 'trim');
        $endTime = input('end_time', '', 'trim');
        $export = input('export', 0, 'intval');
        
        // 构建查询条件
        $where = [];
        
        if ($productId > 0) {
            $where[] = ['il.product_id', '=', $productId];
        }
        
        if ($warehouseId > 0) {
            $where[] = ['il.warehouse_id', '=', $warehouseId];
        }
        
        if ($type !== '') {
            $where[] = ['il.type', '=', intval($type)];
        }
        
        if (!empty($startTime)) {
            $where[] = ['il.operation_time', '>=', strtotime($startTime)];
        }
        
        if (!empty($endTime)) {
            $where[] = ['il.operation_time', '<=', strtotime($endTime . ' 23:59:59')];
        }
        
        // 如果是导出，则生成Excel报表
        if ($export) {
            return $this->exportLogs($where);
        }
        
        // 查询库存日志
        $list = Db::name('inventory_log')
            ->alias('il')
            ->join('product p', 'il.product_id = p.id')
            ->join('warehouse w', 'il.warehouse_id = w.id')
            ->leftJoin('warehouse_location l', 'il.location_id = l.id')
            ->leftJoin('admin a', 'il.operation_by = a.id')
            ->field('il.*, p.title as product_name, p.material_code as product_code, p.specs as product_spec, 
                w.name as warehouse_name, l.name as location_name, a.name as operation_name, DATE_FORMAT(FROM_UNIXTIME(il.create_time), "%Y-%m-%d %H:%i:%s") as create_time')
            ->where($where)
            ->order('il.create_time', 'desc')
            ->paginate([
                'list_rows' => input('limit', 15),
                'page' => input('page', 1),
            ]);
           

          
        
        // 加载仓库列表
        $warehouses = WarehouseModel::where('status', 1)->select();

        // 检查是否为AJAX请求
        if (request()->isAjax()) {
            // 处理分页数据为layui表格所需格式
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ]);
        }

        // 操作类型列表
        $typeList = [
            ['value' => '1', 'label' => '入库'],
            ['value' => '2', 'label' => '出库'],
            ['value' => '3', 'label' => '盘点'],
            ['value' => '4', 'label' => '调拨-调出'],
            ['value' => '5', 'label' => '调拨-调入'],
            ['value' => '6', 'label' => '其他'],
            ['value' => '7', 'label' => '预占'],
            ['value' => '8', 'label' => '预占转锁定'],
            ['value' => '9', 'label' => '预占释放'],
        ];
        
        // 模板赋值
        View::assign([
            'list' => $list,
            'warehouses' => $warehouses,
            'type_list' => $typeList,
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'type' => $type,
            'start_time' => $startTime,
            'end_time' => $endTime
        ]);
        
        // 返回视图
        return View::fetch();
    }
    
    /**
     * 库存预警查询
     */
    public function alerts()
    {
        // 获取查询参数
        $warehouseId = input('warehouse_id', 0, 'intval');
        $alertType = input('alert_type', '', 'trim');
        $keyword = input('keyword', '', 'trim');
        
        // 构建查询条件
        $where = [];
        if ($warehouseId > 0) {
            $where[] = ['sa.warehouse_id', '=', $warehouseId];
        }
        
        if (!empty($alertType)) {
            $where[] = ['sa.alert_type', '=', $alertType];
        }
        
        if (!empty($keyword)) {
            // 在产品名称、编码中搜索关键词
            $product_ids = ProductModel::where('name|code', 'like', '%' . $keyword . '%')->column('id');
            if (!empty($product_ids)) {
                $where[] = ['sa.product_id', 'in', $product_ids];
            }
        }
        
        // 获取库存预警记录
        $list = Db::name('stock_alert')
            ->alias('sa')
            ->join('product p', 'sa.product_id = p.id')
            ->leftJoin('warehouse w', 'sa.warehouse_id = w.id')
            ->field('sa.*, p.name as product_name, p.code as product_code, p.spec as product_spec, 
                p.unit, w.name as warehouse_name, 
                (SELECT SUM(quantity) FROM oa_inventory WHERE product_id = sa.product_id 
                    AND (sa.warehouse_id = 0 OR warehouse_id = sa.warehouse_id)) as current_quantity')
            ->where($where)
            ->order('sa.id', 'desc')
            ->paginate([
                'list_rows' => input('limit', 15),
                'page' => input('page', 1),
            ]);
            
        // 加载仓库列表
        $warehouses = WarehouseModel::where('status', 1)->select();
        
        // 检查是否为AJAX请求
        if (request()->isAjax()) {
            // 处理分页数据为layui表格所需格式
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ]);
        }
        
        // 模板赋值
        View::assign([
            'list' => $list,
            'warehouses' => $warehouses,
            'warehouse_id' => $warehouseId,
            'alert_type' => $alertType,
            'keyword' => $keyword
        ]);
        
        // 返回视图
        return View::fetch();
    }
    
    /**
     * 导出库存汇总报表
     */
    private function exportSummary($warehouseId, $productId, $keyword)
    {
        
        // 获取所有符合条件的产品ID
        $productIds = [];
        if ($productId > 0) {
            $productIds[] = $productId;
        } elseif (!empty($keyword)) {
            $productIds = ProductModel::where('title|material_code', 'like', '%' . $keyword . '%')->column('id');
        }
        
        // 使用模型方法生成报表数据
        $data = InventoryModel::generateSummaryReport($warehouseId, $productIds);
        
        // 导出为Excel
        $filePath = InventoryModel::exportToExcel($data, 'summary');
        
        // 下载文件
        return download($filePath, '库存汇总报表.xlsx');
    }
    
    /**
     * 导出库存明细报表
     */
    public function exportDetail()
    {
        $warehouseId = input('warehouse_id', 0, 'intval');
        $productId = input('product_id', 0, 'intval');
        
        // 使用模型方法生成报表数据
        $data = InventoryModel::generateDetailReport($warehouseId, $productId);
        
        // 导出为Excel
        $filePath = InventoryModel::exportToExcel($data, 'detail');
        
        // 下载文件
        return download($filePath, '库存明细报表.xlsx');
    }
    
    /**
     * 导出库存日志报表
     */
    private function exportLogs($where)
    {
        // 查询所有符合条件的库存日志
        $data = Db::name('inventory_log')
            ->alias('il')
            ->join('product p', 'il.product_id = p.id')
            ->join('warehouse w', 'il.warehouse_id = w.id')
            ->leftJoin('warehouse_location l', 'il.location_id = l.id')
            ->leftJoin('admin a', 'il.operation_by = a.id')
            ->field('il.*, p.title as product_name, p.material_code as product_code, p.specs as product_spec, 
                p.unit, w.name as warehouse_name, l.name as location_name, a.name as operation_name')
            ->where($where)
            ->order('il.operation_time', 'desc')
            ->select()
            ->toArray();
        
        // 创建Excel对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置表头
        $sheet->setCellValue('A1', '操作时间');
        $sheet->setCellValue('B1', '产品编码');
        $sheet->setCellValue('C1', '产品名称');
        $sheet->setCellValue('D1', '产品规格');
        $sheet->setCellValue('E1', '仓库');
        $sheet->setCellValue('F1', '库位');
        $sheet->setCellValue('G1', '批次号');
        $sheet->setCellValue('H1', '操作类型');
        $sheet->setCellValue('I1', '操作数量');
        $sheet->setCellValue('J1', '操作前数量');
        $sheet->setCellValue('K1', '操作后数量');
        $sheet->setCellValue('L1', '关联单据');
        $sheet->setCellValue('M1', '操作人');
        $sheet->setCellValue('N1', '备注');
        
        // 设置表头样式
        $headerStyle = [
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        
        $sheet->getStyle('A1:N1')->applyFromArray($headerStyle);
        
        // 填充数据
        $row = 2;
        foreach ($data as $item) {
            $sheet->setCellValue('A' . $row, date('Y-m-d H:i:s', $item['operation_time']));
            $sheet->setCellValue('B' . $row, $item['product_code']);
            $sheet->setCellValue('C' . $row, $item['product_name']);
            $sheet->setCellValue('D' . $row, $item['product_spec']);
            $sheet->setCellValue('E' . $row, $item['warehouse_name']);
            $sheet->setCellValue('F' . $row, $item['location_name']);
            $sheet->setCellValue('G' . $row, $item['batch_no']);
            
            // 设置操作类型
            $typeText = '';
            switch ($item['type']) {
                case 1: $typeText = '入库'; break;
                case 2: $typeText = '出库'; break;
                case 3: $typeText = '盘点'; break;
                case 4: $typeText = '调拨-调出'; break;
                case 5: $typeText = '调拨-调入'; break;
                case 6: $typeText = '其他'; break;
                default: $typeText = '未知';
            }
            $sheet->setCellValue('H' . $row, $typeText);
            
            $sheet->setCellValue('I' . $row, $item['quantity']);
            $sheet->setCellValue('J' . $row, $item['before_quantity']);
            $sheet->setCellValue('K' . $row, $item['after_quantity']);
            $sheet->setCellValue('L' . $row, ($item['related_bill_type'] ? $item['related_bill_type'] . '：' : '') . $item['related_bill_no']);
            $sheet->setCellValue('M' . $row, $item['operation_name']);
            $sheet->setCellValue('N' . $row, $item['notes']);
            
            $row++;
        }
        
        // 调整列宽
        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(20);
        $sheet->getColumnDimension('E')->setWidth(15);
        $sheet->getColumnDimension('F')->setWidth(15);
        $sheet->getColumnDimension('G')->setWidth(15);
        $sheet->getColumnDimension('H')->setWidth(12);
        $sheet->getColumnDimension('I')->setWidth(12);
        $sheet->getColumnDimension('J')->setWidth(12);
        $sheet->getColumnDimension('K')->setWidth(12);
        $sheet->getColumnDimension('L')->setWidth(20);
        $sheet->getColumnDimension('M')->setWidth(15);
        $sheet->getColumnDimension('N')->setWidth(30);
        
        // 冻结首行
        $sheet->freezePane('A2');
        
        // 保存Excel文件
        $savePath = runtime_path() . 'temp/';
        if(!is_dir($savePath)) {
            mkdir($savePath, 0777, true);
        }
        $fileName = '库存日志报表_' . date('YmdHis') . '.xlsx';
        $filePath = $savePath . $fileName;
        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($filePath);
        
        // 下载文件
        return download($filePath, '库存日志报表.xlsx');
    }
    
    /**
     * 库存报表生成
     */
    public function report()
    {
        // 获取查询参数
        $warehouseId = input('warehouse_id', 0, 'intval');
        $reportType = input('report_type', 'summary', 'trim');
        
        // 加载仓库列表
        $warehouses = WarehouseModel::where('status', 1)->select();
        
        // 模板赋值
        View::assign([
            'warehouses' => $warehouses,
            'warehouse_id' => $warehouseId,
            'report_type' => $reportType
        ]);
        
        // 返回视图
        return View::fetch();
    }
    
    /**
     * 生成报表
     */
    public function generateReport()
    {
        $warehouseId = input('warehouse_id', 0, 'intval');
        $reportType = input('report_type', 'summary', 'trim');
        $reportDate = input('report_date', date('Y-m-d'), 'trim');
        
        // 检查报表类型
        if (!in_array($reportType, ['summary', 'detail'])) {
            return json(['code' => 1, 'msg' => '不支持的报表类型']);
        }
        
        try {
            // 生成报表数据
            $data = [];
            $reportNo = 'INV' . date('YmdHis') . rand(100, 999);
            
            if ($reportType == 'summary') {
                $data = InventoryModel::generateSummaryReport($warehouseId);
            } else {
                $data = InventoryModel::generateDetailReport($warehouseId);
            }
            
            // 保存报表记录
            $reportId = Db::name('inventory_report')->insertGetId([
                'report_no' => $reportNo,
                'report_type' => $reportType == 'summary' ? 1 : 2,
                'report_date' => $reportDate,
                'report_data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'created_by' => session('admin.id'),
                'create_time' => time()
            ]);
            
            return json(['code' => 0, 'msg' => '报表生成成功', 'data' => ['report_id' => $reportId]]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '报表生成失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 导出库存数据
     * @param array $where 查询条件
     * @return \think\response\File
     */
    private function exportInventory($where)
    {
        // 创建Excel对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('库存列表');
        
        // 设置表头
        $sheet->setCellValue('A1', '产品编码');
        $sheet->setCellValue('B1', '产品名称');
        $sheet->setCellValue('C1', '产品规格');
        $sheet->setCellValue('D1', '仓库');
        $sheet->setCellValue('E1', '库位');
        $sheet->setCellValue('F1', '批次号');
        $sheet->setCellValue('G1', '库存数量');
        $sheet->setCellValue('H1', '可用数量');
        $sheet->setCellValue('I1', '锁定数量');
        $sheet->setCellValue('J1', '已分配数量');
        $sheet->setCellValue('K1', '单位');
        $sheet->setCellValue('L1', '成本价');
        $sheet->setCellValue('M1', '状态');
        
        // 获取所有库存数据
        $list = InventoryModel::alias('i')
            ->join('product p', 'i.product_id = p.id')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->leftJoin('warehouse_location l', 'i.location_id = l.id')
            ->field('i.*, p.title as product_name, p.material_code as product_code, p.specs as product_spec, 
                w.name as warehouse_name, l.name as location_name, p.unit,
                (SELECT IFNULL(SUM(quantity), 0) FROM oa_inventory_lock WHERE inventory_id = i.id AND status = 1) as locked_quantity')
            ->where($where)
            ->order('i.id', 'desc')
            ->select();
        
        // 填充数据
        $row = 2;
        foreach ($list as $item) {
            $sheet->setCellValue('A' . $row, $item['product_code']);
            $sheet->setCellValue('B' . $row, $item['product_name']);
            $sheet->setCellValue('C' . $row, $item['product_spec']);
            $sheet->setCellValue('D' . $row, $item['warehouse_name']);
            $sheet->setCellValue('E' . $row, $item['location_name']);
            $sheet->setCellValue('F' . $row, $item['batch_no']);
            $sheet->setCellValue('G' . $row, $item['quantity']);
            $sheet->setCellValue('H' . $row, $item['available_quantity']);
            $sheet->setCellValue('I' . $row, $item['locked_quantity'] ?: 0);
            $sheet->setCellValue('J' . $row, $item['allocated_quantity']);
            $sheet->setCellValue('K' . $row, $item['unit']);
            $sheet->setCellValue('L' . $row, $item['cost_price']);
            
            // 状态转换
            $status = '';
            switch ($item['status']) {
                case 1:
                    $status = '正常';
                    break;
                case 2:
                    $status = '冻结';
                    break;
                case 3:
                    $status = '待检';
                    break;
                default:
                    $status = '未知';
            }
            $sheet->setCellValue('M' . $row, $status);
            
            $row++;
        }
        
        // 设置列宽
        $sheet->getColumnDimension('A')->setWidth(15);
        $sheet->getColumnDimension('B')->setWidth(25);
        $sheet->getColumnDimension('C')->setWidth(15);
        $sheet->getColumnDimension('D')->setWidth(15);
        $sheet->getColumnDimension('E')->setWidth(15);
        $sheet->getColumnDimension('F')->setWidth(15);
        $sheet->getColumnDimension('G')->setWidth(10);
        $sheet->getColumnDimension('H')->setWidth(10);
        $sheet->getColumnDimension('I')->setWidth(10);
        $sheet->getColumnDimension('J')->setWidth(10);
        $sheet->getColumnDimension('K')->setWidth(8);
        $sheet->getColumnDimension('L')->setWidth(10);
        $sheet->getColumnDimension('M')->setWidth(10);
        
        // 生成文件
        $filename = '库存列表_' . date('YmdHis') . '.xlsx';
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $savePath = root_path() . 'public/uploads/export/' . $filename;
        
        // 确保目录存在
        $saveDir = dirname($savePath);
        if (!is_dir($saveDir)) {
            mkdir($saveDir, 0777, true);
        }
        
        $writer->save($savePath);
        
        // 下载文件
        return download($savePath, $filename);
    }

    /**
     * 获取产品列表（用于出库单选择产品）
     */
    public function getProductList()
    {
        if (request()->isAjax()) {
            $param = input('param.');
            $warehouseId = isset($param['warehouse_id']) ? intval($param['warehouse_id']) : 0;
            $keyword = isset($param['keyword']) ? trim($param['keyword']) : '';
            
            if (!$warehouseId) {
                return json(['code' => 1, 'msg' => '请选择仓库']);
            }
            
            // 构建查询条件
            $where = [
                ['i.warehouse_id', '=', $warehouseId],
                ['i.quantity', '>', 0],
                ['i.available_quantity', '>', 0]
            ];
            
            // 关键词搜索
            if (!empty($keyword)) {
                $where[] = ['p.material_code|p.title', 'like', '%' . $keyword . '%'];
            }
            
            // 连表查询有库存的产品
            $products = Db::name('inventory')
                ->alias('i')
                ->join('product p', 'i.product_id = p.id', 'inner')
                ->field('p.id, p.material_code, p.title, p.specs, p.unit, 
                       SUM(i.quantity) as total_quantity, 
                       SUM(i.available_quantity) as available_quantity')
                ->where($where)
                ->group('p.id')
                ->order('p.id desc')
                ->paginate([
                    'list_rows' => input('limit', 10),
                    'page' => input('page', 1),
                ]);
            
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $products->total(),
                'data' => $products->items()
            ]);
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }

    /**
     * 获取库存信息（用于前端AJAX请求）
     */
    public function getStockInfo()
    {
        // 使用框架的Request对象而不是类属性
        $request = request();
        
        // 检查是否是AJAX请求
        if (!$request->isAjax()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }
        
        // 获取参数
        $productId = $request->param('product_id', 0, 'intval');
        $warehouseId = $request->param('warehouse_id', 0, 'intval');
        $locationId = $request->param('location_id', 0, 'intval');
        $batchNo = $request->param('batch_no', '');
        
        // 参数验证
        if (empty($productId) || empty($warehouseId)) {
            return json(['code' => 0, 'msg' => '参数错误: 产品ID和仓库ID不能为空']);
        }
        
        // 构建查询条件
        $where = [
            'product_id' => $productId,
            'warehouse_id' => $warehouseId
        ];
        
        if (!empty($locationId)) {
            $where['location_id'] = $locationId;
        }
        
        if (!empty($batchNo)) {
            $where['batch_no'] = $batchNo;
        }
        
        // 查询库存信息
        $inventory = \app\warehouse\model\Inventory::where($where)->find();
        
        if (!$inventory) {
            return json(['code' => 0, 'msg' => '未找到库存记录']);
        }
        
        // 返回库存信息
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'id' => $inventory->id,
                'product_id' => $inventory->product_id,
                'warehouse_id' => $inventory->warehouse_id,
                'location_id' => $inventory->location_id ?: 0,
                'batch_no' => $inventory->batch_no ?: '',
                'quantity' => $inventory->quantity,
                'available_quantity' => $inventory->available_quantity,
                'allocated_quantity' => $inventory->allocated_quantity,
                'locked_quantity' => $inventory->locked_quantity,
                'unit' => $inventory->unit ?: '',
                'status' => $inventory->status
            ]
        ]);
    }

    /**
     * 库存锁定记录列表
     */
    public function lockList()
    {
        // 获取查询参数
        $warehouseId = input('warehouse_id', 0, 'intval');
        $keyword = input('keyword', '', 'trim');
        $status = input('status', '', 'trim');
        
        // 构建查询条件
        $where = [];
        
        if ($warehouseId > 0) {
            $where[] = ['il.warehouse_id', '=', $warehouseId];
        }
        
        if ($status !== '') {
            $where[] = ['il.status', '=', intval($status)];
        }
        
        if (!empty($keyword)) {
            // 产品名称、编码、批次号或单据编号搜索
            $product_ids = ProductModel::where('title|material_code', 'like', '%' . $keyword . '%')->column('id');
            if (!empty($product_ids)) {
                $where[] = ['il.product_id', 'in', $product_ids];
            } else {
                $where[] = ['il.ref_no', 'like', '%' . $keyword . '%'];
            }
        }

        $list = Db::name('inventory_lock')
            ->alias('il')
            ->leftJoin('admin a', 'il.created_by = a.id')
            ->leftJoin('product p', 'il.product_id = p.id')
            ->leftJoin('warehouse w', 'il.warehouse_id = w.id')
            ->leftJoin('warehouse_location l', 'il.location_id = l.id')
            ->field([
                'il.*',
                'a.name as operator_name',
                'p.title as product_name',
                'p.material_code as product_code',
                'p.unit as product_unit',
                'w.name as warehouse_name',
                'l.name as location_name',
                'FROM_UNIXTIME(il.lock_time, "%Y-%m-%d %H:%i:%s") as lock_time_text',
                'FROM_UNIXTIME(il.expire_time, "%Y-%m-%d %H:%i:%s") as expire_time_text'
            ])
            ->where($where)
            ->order('il.id', 'desc')
            ->paginate([
                'list_rows' => input('limit', 15),
                'page' => input('page', 1),
            ]);
    
        // 处理状态显示文本和锁定类型文本
        $statusMap = [
            0 => '已释放',
            1 => '已锁定',
            2 => '已使用',
            3 => '已过期'
        ];
        
        $refTypeMap = [
            'customer_order' => '客户订单',
            'production_order' => '生产订单',
            'transfer_order' => '调拨单',
            'sample_order' => '样品单',
            'manual_lock' => '手动锁定',
            'quality_check' => '质检锁定'
        ];
        
        // 处理数据
        foreach ($list->items() as &$item) {
            $item['status_text'] = $statusMap[$item['status']] ?? '未知';
            $item['ref_type_text'] = $refTypeMap[$item['ref_type']] ?? $item['ref_type'];
        }
        
        // 检查是否为AJAX请求
        if (request()->isAjax()) {
            // 处理分页数据为layui表格所需格式
            $result = [
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ];
            return json($result);
        }
        
        // 获取仓库列表
        $warehouses = WarehouseModel::where('status', 1)->select();
        
        // 状态列表
        $statusList = [
            ['value' => '', 'label' => '全部状态'],
            ['value' => '0', 'label' => '已释放'],
            ['value' => '1', 'label' => '已锁定'],
            ['value' => '2', 'label' => '已使用'],
            ['value' => '3', 'label' => '已过期']
        ];
        
        // 模板赋值
        View::assign([
            'warehouses' => $warehouses,
            'status_list' => $statusList,
            'warehouse_id' => $warehouseId,
            'status' => $status,
            'keyword' => $keyword
        ]);
        
        // 返回视图
        return View::fetch('lockList');
    }
    
    /**
     * 单个库存锁定详情
     */
    public function lockDetail()
    {
        $inventoryId = input('inventory_id', 0, 'intval');
        
        if (empty($inventoryId)) {
            $this->error('参数错误');
        }
        
        // 获取库存信息
        $inventory = InventoryModel::alias('i')
            ->join('product p', 'i.product_id = p.id')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->leftJoin('warehouse_location l', 'i.location_id = l.id')
            ->field('i.*, p.title as product_name, p.material_code as product_code, p.specs as product_spec, 
                     w.name as warehouse_name, l.name as location_name')
            ->where('i.id', $inventoryId)
            ->find();
        
        if (!$inventory) {
            $this->error('库存记录不存在');
        }
        
        // 处理AJAX请求
        if (request()->isAjax()) {
            // 获取锁定记录
            $where = [['inventory_id', '=', $inventoryId]];
            
            $list = Db::name('inventory_lock')
                ->alias('il')
                ->leftJoin('admin a', 'il.created_by = a.id')
                ->field('il.*, a.name as operator_name,
                         FROM_UNIXTIME(il.lock_time, "%Y-%m-%d %H:%i:%s") as lock_time_text,
                         FROM_UNIXTIME(il.expire_time, "%Y-%m-%d %H:%i:%s") as expire_time_text')
                ->where($where)
                ->order('il.id', 'desc')
                ->paginate([
                    'list_rows' => input('limit', 15),
                    'page' => input('page', 1),
                ]);
            
            // 处理ref_type显示文本
            $refTypeMap = [
                'outbound' => '出库单',
                'production' => '生产领料',
                'transfer' => '调拨',
                'sample' => '样品',
                'inventory' => '盘点'
            ];
            
            // 处理数据
            foreach ($list->items() as &$item) {
                $item['ref_type_text'] = isset($refTypeMap[$item['ref_type']]) ? $refTypeMap[$item['ref_type']] : $item['ref_type'];
            }
            
            $result = [
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ];
            return json($result);
        }
        
        // 模板赋值
        View::assign([
            'inventory' => $inventory
        ]);
        
        // 返回视图
        return View::fetch('lock_detail');
    }
    
    /**
     * 单个库存锁定详情（兼容旧路径）
     */
    public function lock_detail()
    {
        return $this->inventoryreserveDetail();
    }
      /**
     * 预占库存详情
     */
    
    public function inventoryreserveDetail()
    {
        $inventoryId = input('inventory_id', 0, 'intval');
        
        if (empty($inventoryId)) {
            $this->error('参数错误');
        }
        
        // 获取库存信息
        $inventory = InventoryModel::alias('i')
            ->join('product p', 'i.product_id = p.id')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->leftJoin('warehouse_location l', 'i.location_id = l.id')
            ->field('i.*, p.title as product_name, p.material_code as product_code, p.specs as product_spec, 
                     w.name as warehouse_name, l.name as location_name')
            ->where('i.id', $inventoryId)
            ->find();
        
        if (!$inventory) {
            $this->error('库存记录不存在');
        }
        
        // 处理AJAX请求
        if (request()->isAjax()) {
            // 获取锁定记录 allocated_quantity 
            $where = [['inventory_id', '=', $inventoryId]];
            
            $list = Db::name('inventory_reserve')
            ->alias('ir')
            ->leftJoin('admin a', 'ir.created_by = a.id')
            ->leftJoin('customer_order_detail cod', 'ir.ref_id = cod.id')
            ->leftJoin('customer_order co', 'cod.order_id = co.id')
            ->leftJoin('customer c', 'co.customer_id = c.id')
            ->leftJoin('purchase_receipt wr', 'ir.receipt_id = wr.id')
            ->field([
                'ir.*',
                'a.name as operator_name',
                'FROM_UNIXTIME(ir.create_time, "%Y-%m-%d") as create_time_text',
                'co.order_no as customer_order_no',
                'c.name as customer_name',
                'wr.receipt_no'
            ])
            ->where($where)
            ->order('ir.id', 'desc')
            ->paginate([
                'list_rows' => input('limit', 15),
                'page' => input('page', 1),
            ]);
        
        // 处理状态显示文本
        $statusMap = [
            0 => '无效',
            1 => '有效',
            2 => '已出库'
        ];
            
            // 处理数据
            foreach ($list->items() as &$item) {
                $item['ref_type_text'] = isset($refTypeMap[$item['ref_type']]) ? $refTypeMap[$item['ref_type']] : $item['ref_type'];
               
            }
          
           
            
            $result = [
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ];
            return json($result);
        }
        
        // 模板赋值
        View::assign([
            'inventory' => $inventory
        ]);
        
        // 返回视图
        return View::fetch('lock_detail');
    }
   
    //删除预占库存
    public function delLock()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $id = input('id', 0, 'intval');
        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 查询预占记录 inventory_id
        $reserve = Db::name('inventory_reserve')->where('id', $id)->find();
        if (!$reserve) {
            return json(['code' => 1, 'msg' => '预占记录不存在']);
        }

        // 检查状态是否为已出库
        if ($reserve['status'] == 2) {
            return json(['code' => 1, 'msg' => '该预占记录已出库，不能删除']);
        }

        Db::startTrans();
        try {
        
            // 删除预占记录
            $result = Db::name('inventory_reserve')->where('id', $id)->delete();
            if ($result === false) {
                throw new \Exception('删除预占记录失败');
            }

             

            Db::commit();
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
     
    /**
     * 获取产品库存状态API
     */
    public function getProductInventory()
    {
        if (request()->isAjax()) {
            $param = input('param.');
            
            // 验证参数
            if (empty($param['product_id'])) {
                return json(['code' => 1, 'msg' => '产品ID不能为空']);
            }
            
            $productId = intval($param['product_id']);
            $warehouseId = isset($param['warehouse_id']) ? intval($param['warehouse_id']) : 0;
            
            // 使用库存锁定服务获取库存状态
            $lockService = new InventoryLockService();
            $result = $lockService->getProductInventoryStatus($productId, $warehouseId);
            
            return json(['code' => 0, 'msg' => '获取成功', 'data' => $result]);
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 获取产品可用库存量API（简化版，只返回数量）
     */
    public function getProductAvailableQuantity()
    {
        if (request()->isAjax()) {
            $param = input('param.');
            
            // 验证参数
            if (empty($param['product_id'])) {
                return json(['code' => 1, 'msg' => '产品ID不能为空']);
            }
            
            $productId = intval($param['product_id']);
            $warehouseId = isset($param['warehouse_id']) ? intval($param['warehouse_id']) : 0;
            
            // 使用库存锁定服务获取可用数量
            $lockService = new InventoryLockService();
            $result = $lockService->getAvailableQuantity($productId, $warehouseId);
            
            return json(['code' => 0, 'msg' => '获取成功', 'data' => $result]);
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 检查产品是否有足够库存API
     */
    public function checkProductStock()
    {
        if (request()->isAjax()) {
            $param = input('param.');
            
            // 验证参数
            if (empty($param['product_id'])) {
                return json(['code' => 1, 'msg' => '产品ID不能为空']);
            }
            
            if (!isset($param['quantity']) || $param['quantity'] <= 0) {
                return json(['code' => 1, 'msg' => '数量必须大于0']);
            }
            
            $productId = intval($param['product_id']);
            $quantity = floatval($param['quantity']);
            $warehouseId = isset($param['warehouse_id']) ? intval($param['warehouse_id']) : 0;
            
            // 使用库存锁定服务检查库存
            $lockService = new InventoryLockService();
            $hasEnough = $lockService->hasEnoughStock($productId, $quantity, $warehouseId);
            
            return json([
                'code' => 0, 
                'msg' => '检查完成', 
                'data' => [
                    'has_enough' => $hasEnough,
                    'status' => $hasEnough ? 'sufficient' : 'insufficient'
                ]
            ]);
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 锁定库存API
     */
    public function lockInventory()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        $param = input('param.');
        
        // 验证必要参数
        $requiredFields = ['product_id', 'quantity', 'warehouse_id', 'ref_type', 'ref_id'];
        foreach ($requiredFields as $field) {
            if (empty($param[$field])) {
                return json(['code' => 1, 'msg' => "参数 {$field} 不能为空"]);
            }
        }
        
        try {
            $lockService = new InventoryLockService();
            $result = $lockService->lockInventory([
                'product_id' => intval($param['product_id']),
                'quantity' => floatval($param['quantity']),
                'warehouse_id' => intval($param['warehouse_id']),
                'location_id' => intval($param['location_id'] ?? 0),
                'batch_no' => $param['batch_no'] ?? '',
                'ref_type' => $param['ref_type'],
                'ref_id' => intval($param['ref_id']),
                'ref_no' => $param['ref_no'] ?? '',
                'notes' => $param['notes'] ?? '',
                'expire_time' => !empty($param['expire_time']) ? strtotime($param['expire_time']) : 0,
                'auto_release' => intval($param['auto_release'] ?? 1),
                'release_condition' => $param['release_condition'] ?? '',
                'created_by' => session('admin.id')
            ]);
            
            return json($result);
            
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '锁定失败：' . $e->getMessage()]);
        }
    }
} 