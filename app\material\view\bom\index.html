{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.bom-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    color: #fff;
}
.status-draft { background-color: #cc8b11; }
.status-approved { background-color: #5FB878; }
.status-disabled { background-color: #FF5722; }
</style>
{/block}

<!-- 主体 -->
{block name="body"}
<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部</li>
                <li>草稿</li>
                <li>已启用</li>
                <li>已停用</li>
            </ul>
        </div> 
    </div>
    
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" name="bom_code" placeholder="请输入BOM编号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:180px;">
            <input type="text" name="product_code" placeholder="产品编号/产品名称" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search">
                <i class="layui-icon layui-icon-search mr-1"></i>搜索
            </button>
        </div>
        <div class="layui-input-inline">
            <button type="reset" class="layui-btn layui-btn-primary" lay-filter="table-reset">重置</button>
        </div>
    </form>
    
    <table class="layui-hide" id="table_bom" lay-filter="table_bom"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <span>+ 新增</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="copy">
        <span>复制</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="export">
        <span>批量导出</span>
    </button>
  
  </div>
</script>

<script type="text/html" id="statusTpl">
{{# if(d.status == 1){ }}
    <span class="bom-status status-approved">已启用</span>
{{# } else if(d.status == 0){ }}
    <span class="bom-status status-draft">草稿</span>
    {{# } else if(d.status == 2){ }}
      <span class="bom-status status-disabled">停用</span>
{{# } }}
</script>

<script type="text/html" id="operationTpl">
<div class="layui-btn-group">
    <span class="layui-btn layui-btn-xs" lay-event="view">详细</span>
    <span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>
    <span class="layui-btn layui-btn-xs layui-btn-normal" lay-event="changeStatus">
        {{# if(d.status == 0){ }}
            启用
        {{# } else if(d.status == 1){ }}
            禁用
        {{# } else if(d.status == 2){ }}
            启用
        {{# } }}
    </span>
</div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','tablePlus'];
function gouguInit() {
    var table = layui.tablePlus, element = layui.element, tool = layui.tool;
    
    // tab切换
    element.on('tab(tab)', function(data){
        var statusMap = {"0": "", "1": "0", "2": "1", "3": "2"};
        $('[name="tab"]').val(data.index);
        $("#barsearchform")[0].reset();
        layui.pageTable.reload({where:{status:statusMap[data.index]},page:{curr:1}});
        return false;
    });
    
    layui.pageTable = table.render({
        elem: "#table_bom"
        ,title: "BOM列表"
        ,toolbar: "#toolbarDemo"
        ,url: "/material/bom/index"
        ,page: true
        ,limit: 20
        ,cellMinWidth: 80
        ,height: 'full-152'
        ,cols: [[ //表头
            {type: 'checkbox', fixed: 'left'}
            ,{
                field: 'id',
                title: 'ID',
                align: 'center',
                width: 80
            }
            ,{
                field: 'bom_code',
                title: 'BOM编号',
                align: 'center',
                width: 300
            }
            ,{
                field: 'bom_name',
                title: '标准BOM',
                align: 'center',
                width: 200
            }
            ,{
                field: 'product_name',
                title: '产品名称',
                width: 300
            }
            ,{
                field: 'customer_name',
                title: '客户名称',
                align: 'center',
                width: 300
            }
            ,{
                field: 'status',
                title: '状态',
                align: 'center',
                width: 100,
                templet: '#statusTpl'
            }
            ,{
                field: 'create_user',
                title: '创建人',
                align: 'center',
                width: 100
            }
            ,{
                field: 'create_time_formatted',
                title: '创建时间',
                align: 'center',
                width: 160
            }
            ,{
                field: 'right',
                fixed:'right',
                title: '操作',
                minwidth: 200,
                align: 'center',
                ignoreExport:true,
                templet: '#operationTpl'
            }
        ]]
    });
    
    // 表头工具栏事件
    table.on('toolbar(table_bom)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;
        
        if (obj.event === 'add'){
            tool.box("/material/bom/add", "新增BOM", "95%", "85%");
            return;
        }
        if (obj.event === 'copy'){
            if(data.length === 0){
                layer.msg('请选择要复制的数据');
                return;
            }
            if(data.length > 1){
                layer.msg('只能选择一条数据进行复制');
                return;
            }

            layer.confirm('确定要复制选中的BOM吗？复制后的BOM状态将设为草稿。', { icon: 3, title: '提示' }, function (index) {
                tool.post("/material/bom/copy", {id: data[0].id}, function(res){
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layui.pageTable.reload();
                        layer.msg('复制成功，新BOM已创建为草稿状态', {icon: 1});
                    }
                });
                layer.close(index);
            });
            return;
        }
        if (obj.event === 'export'){
            if(data.length === 0){
                layer.msg('请选择要导出的数据');
                return;
            }
            var ids = [];
            layui.each(data, function(index, item){
                ids.push(item.id);
            });
            tool.post("/material/bom/export", {ids: ids}, function(res){
                layer.msg(res.msg);
            });
            return;
        }
        if (obj.event === 'batchDelete'){
            if(data.length === 0){
                layer.msg('请选择要删除的数据');
                return;
            }
            layer.confirm('确定要删除选中的BOM吗?', { icon: 3, title: '提示' }, function (index) {
                var ids = [];
                layui.each(data, function(index, item){
                    ids.push(item.id);
                });
                tool.post("/material/bom/batchDelete", {ids: ids}, function(res){
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layui.pageTable.reload();
                    }
                });
                layer.close(index);
            });
            return;
        }
    });
    
    // 行工具事件
    table.on('tool(table_bom)',function (obj) {
        var data = obj.data;
        
        if (obj.event === 'copy') {
            layer.confirm('确定要复制该BOM吗？复制后的BOM状态将设为草稿。', { icon: 3, title: '提示' }, function (index) {
                tool.post("/material/bom/copy", {id: data.id}, function(res){
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layui.pageTable.reload();
                        layer.msg('复制成功，新BOM已创建为草稿状态', {icon: 1});
                    }
                });
                layer.close(index);
            });
            return;
        }
        if (obj.event === 'view') {
            tool.box("/material/bom/view?id=" + data.id, "查看BOM", "95%", "85%");
            return;
        }
        if (obj.event === 'edit') {
             tool.box("/material/bom/edit?id=" + data.id, "编辑BOM", "95%", "85%");
            return;
        }
        if (obj.event === 'changeStatus') {
            var statusText = '';
            var newStatus = '';
            var confirmText = '';

            if (data.status == 0) {
                statusText = '启用';
                newStatus = 1;
                confirmText = '确定要启用该BOM吗？启用后BOM将可以正常使用。';
            } else if (data.status == 1) {
                statusText = '禁用';
                newStatus = 2;
                confirmText = '确定要禁用该BOM吗？禁用后BOM将不可使用。';
            } else if (data.status == 2) {
                statusText = '启用';
                newStatus = 1;
                confirmText = '确定要重新启用该BOM吗？';
            }

            layer.confirm(confirmText, { icon: 3, title: '提示' }, function (index) {
                tool.post("/material/bom/changeStatus", {id: data.id, status: newStatus}, function(res){
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        layui.pageTable.reload();
                    }
                });
                layer.close(index);
            });
            return;
        }
        if (obj.event === 'export') {
            tool.post("/material/bom/export", {ids: [data.id]}, function(res){
                layer.msg(res.msg);
            });
            return;
        }
        if (obj.event === 'delete') {
            layer.confirm('确定要删除该BOM吗?', { icon: 3, title: '提示' }, function (index) {
                tool.post("/material/bom/delete", { id: data.id }, function(res){
                    layer.msg(res.msg);
                    if (res.code == 0) {
                        obj.del();
                    }
                });
                layer.close(index);
            });
            return;
        }
    });
}
</script>
{/block}
<!-- /脚本 -->