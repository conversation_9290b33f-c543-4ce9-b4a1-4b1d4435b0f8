<?php
declare (strict_types = 1);

namespace app\warehouse\service;

use think\facade\Db;
use think\facade\Log;
use app\warehouse\model\Inventory;
use app\warehouse\model\InventoryLock;

/**
 * 库存锁定服务类
 * 提供完整的库存锁定管理功能
 */
class InventoryLockService
{
    // 锁定类型常量
    const LOCK_TYPE_ORDER = 'customer_order';        // 客户订单锁定
    const LOCK_TYPE_PRODUCTION = 'production_order'; // 生产订单锁定
    const LOCK_TYPE_TRANSFER = 'transfer_order';     // 调拨单锁定
    const LOCK_TYPE_SAMPLE = 'sample_order';         // 样品单锁定
    const LOCK_TYPE_MANUAL = 'manual_lock';          // 手动锁定
    const LOCK_TYPE_QUALITY = 'quality_check';       // 质检锁定

    // 锁定状态常量
    const LOCK_STATUS_RELEASED = 0;    // 已释放
    const LOCK_STATUS_LOCKED = 1;      // 已锁定
    const LOCK_STATUS_USED = 2;        // 已使用
    const LOCK_STATUS_EXPIRED = 3;     // 已过期

    // 操作类型常量
    const OPERATION_LOCK = 'lock';       // 锁定
    const OPERATION_RELEASE = 'release'; // 释放
    const OPERATION_USE = 'use';         // 使用
    const OPERATION_EXPIRE = 'expire';   // 过期

    /**
     * 获取产品库存状态
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID，0表示所有仓库
     * @return array
     */
    public function getProductInventoryStatus($productId, $warehouseId = 0)
    {
        $where = ['product_id' => $productId];
        if ($warehouseId > 0) {
            $where['warehouse_id'] = $warehouseId;
        }

        // 获取实际库存总量
        $totalQuantity = Inventory::where($where)->sum('quantity');

        // 获取锁定总量（有效状态）
        $lockedQuantity = $this->getLockedQuantity($productId, $warehouseId);

        // 计算可用库存
        $availableQuantity = $totalQuantity - $lockedQuantity;

        return [
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'total_quantity' => $totalQuantity,
            'locked_quantity' => $lockedQuantity,
            'available_quantity' => max(0, $availableQuantity),
            'status' => $availableQuantity > 0 ? 'sufficient' : 'insufficient'
        ];
    }

    /**
     * 获取产品锁定数量
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @return float
     */
    public function getLockedQuantity($productId, $warehouseId = 0)
    {
        $where = [
            'product_id' => $productId,
            'status' => self::LOCK_STATUS_LOCKED
        ];
        
        if ($warehouseId > 0) {
            $where['warehouse_id'] = $warehouseId;
        }

        return Db::name('inventory_lock')->where($where)->sum('quantity') ?: 0;
    }

    /**
     * 获取产品可用库存数量
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @return float
     */
    public function getAvailableQuantity($productId, $warehouseId = 0)
    {
        $status = $this->getProductInventoryStatus($productId, $warehouseId);
        return $status['available_quantity'];
    }

    /**
     * 检查产品是否有足够库存
     * @param int $productId 产品ID
     * @param float $quantity 需要数量
     * @param int $warehouseId 仓库ID
     * @return bool
     */
    public function hasEnoughStock($productId, $quantity, $warehouseId = 0)
    {
        $availableQuantity = $this->getAvailableQuantity($productId, $warehouseId);
        return $availableQuantity >= $quantity;
    }

    /**
     * 锁定库存
     * @param array $params 锁定参数
     * @return array
     */
    public function lockInventory($params)
    {
        $productId = $params['product_id'];
        $quantity = $params['quantity'];
        $warehouseId = $params['warehouse_id'] ?? 0;
        $locationId = $params['location_id'] ?? 0;
        $batchNo = $params['batch_no'] ?? '';
        $refType = $params['ref_type'];
        $refId = $params['ref_id'];
        $refNo = $params['ref_no'] ?? '';
        $notes = $params['notes'] ?? '';
        $expireTime = $params['expire_time'] ?? 0;
        $autoRelease = $params['auto_release'] ?? 1;
        $releaseCondition = $params['release_condition'] ?? '';
        $createdBy = $params['created_by'] ?? 0;

        // 检查库存是否充足
        if (!$this->hasEnoughStock($productId, $quantity, $warehouseId)) {
            throw new \Exception('库存不足，无法锁定');
        }

        // 获取具体的库存记录
        $inventory = $this->findBestInventoryForLock($productId, $warehouseId, $locationId, $batchNo, $quantity);
        if (!$inventory) {
            throw new \Exception('未找到合适的库存记录进行锁定');
        }

        Db::startTrans();
        try {
            // 创建锁定记录
            $lockData = [
                'inventory_id' => $inventory['id'],
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'location_id' => $locationId,
                'batch_no' => $batchNo,
                'quantity' => $quantity,
                'unit' => $inventory['unit'] ?? '',
                'cost_price' => $inventory['cost_price'] ?? 0,
                'ref_type' => $refType,
                'ref_id' => $refId,
                'ref_no' => $refNo,
                'lock_time' => time(),
                'expire_time' => $expireTime,
                'auto_release' => $autoRelease,
                'release_condition' => $releaseCondition,
                'status' => self::LOCK_STATUS_LOCKED,
                'created_by' => $createdBy,
                'create_time' => time(),
                'update_time' => time(),
                'remark' => $notes
            ];

            $lockId = Db::name('inventory_lock')->insertGetId($lockData);

            // 记录锁定日志
            $this->addLockLog([
                'lock_id' => $lockId,
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'operation_type' => self::OPERATION_LOCK,
                'quantity' => $quantity,
                'before_quantity' => 0,
                'after_quantity' => $quantity,
                'ref_type' => $refType,
                'ref_id' => $refId,
                'ref_no' => $refNo,
                'notes' => "锁定库存：{$notes}",
                'created_by' => $createdBy
            ]);

            Db::commit();

            return [
                'code' => 0,
                'msg' => '锁定成功',
                'data' => ['lock_id' => $lockId]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 批量锁定库存
     * @param array $lockItems 锁定项目数组
     * @return array
     */
    public function batchLockInventory($lockItems)
    {
        $lockIds = [];
        $errors = [];

        Db::startTrans();
        try {
            foreach ($lockItems as $item) {
                try {
                    $result = $this->lockInventory($item);
                    $lockIds[] = $result['data']['lock_id'];
                } catch (\Exception $e) {
                    $errors[] = [
                        'product_id' => $item['product_id'],
                        'error' => $e->getMessage()
                    ];
                }
            }

            if (!empty($errors)) {
                Db::rollback();
                return [
                    'code' => 1,
                    'msg' => '批量锁定失败',
                    'data' => ['errors' => $errors]
                ];
            }

            Db::commit();
            return [
                'code' => 0,
                'msg' => '批量锁定成功',
                'data' => ['lock_ids' => $lockIds]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 使用锁定库存（出库时调用）
     * @param array $lockIds 锁定记录ID数组
     * @param array $options 选项参数
     * @return array
     */
    public function useLockedInventory($lockIds, $options = [])
    {
        if (empty($lockIds)) {
            throw new \Exception('锁定记录ID不能为空');
        }

        Db::startTrans();
        try {
            $totalUsedQuantity = 0;
            $usedLocks = [];

            foreach ($lockIds as $lockId) {
                $lock = Db::name('inventory_lock')->where('id', $lockId)->lock(true)->find();
                
                if (!$lock) {
                    throw new \Exception("锁定记录不存在：{$lockId}");
                }

                if ($lock['status'] != self::LOCK_STATUS_LOCKED) {
                    throw new \Exception("锁定记录状态异常：{$lockId}");
                }

                // 更新锁定状态为已使用
                $updated = Db::name('inventory_lock')
                    ->where('id', $lockId)
                    ->update([
                        'status' => self::LOCK_STATUS_USED,
                        'update_time' => time()
                    ]);

                if (!$updated) {
                    throw new \Exception("更新锁定状态失败：{$lockId}");
                }

                // 减少实际库存
                $inventory = Inventory::find($lock['inventory_id']);
                if ($inventory && $inventory->quantity >= $lock['quantity']) {
                    $beforeQuantity = $inventory->quantity;
                    $inventory->quantity -= $lock['quantity'];
                    $inventory->save();

                    // 记录库存变动日志
                    $this->addInventoryLog([
                        'product_id' => $lock['product_id'],
                        'warehouse_id' => $lock['warehouse_id'],
                        'location_id' => $lock['location_id'],
                        'batch_no' => $lock['batch_no'],
                        'type' => 2, // 出库
                        'quantity' => $lock['quantity'],
                        'before_quantity' => $beforeQuantity,
                        'after_quantity' => $inventory->quantity,
                        'related_bill_type' => $lock['ref_type'],
                        'related_bill_no' => $lock['ref_no'],
                        'operation_by' => $options['operation_by'] ?? 0,
                        'operation_time' => time(),
                        'notes' => '使用锁定库存出库'
                    ]);
                }

                // 记录锁定使用日志
                $this->addLockLog([
                    'lock_id' => $lockId,
                    'product_id' => $lock['product_id'],
                    'warehouse_id' => $lock['warehouse_id'],
                    'operation_type' => self::OPERATION_USE,
                    'quantity' => $lock['quantity'],
                    'before_quantity' => $lock['quantity'],
                    'after_quantity' => 0,
                    'ref_type' => $lock['ref_type'],
                    'ref_id' => $lock['ref_id'],
                    'ref_no' => $lock['ref_no'],
                    'notes' => '使用锁定库存',
                    'created_by' => $options['operation_by'] ?? 0
                ]);

                $totalUsedQuantity += $lock['quantity'];
                $usedLocks[] = $lock;
            }

            Db::commit();

            return [
                'code' => 0,
                'msg' => '使用锁定库存成功',
                'data' => [
                    'total_used_quantity' => $totalUsedQuantity,
                    'used_locks' => $usedLocks
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 释放锁定库存
     * @param array $lockIds 锁定记录ID数组
     * @param array $options 选项参数
     * @return array
     */
    public function releaseLockedInventory($lockIds, $options = [])
    {
        if (empty($lockIds)) {
            throw new \Exception('锁定记录ID不能为空');
        }

        Db::startTrans();
        try {
            $totalReleasedQuantity = 0;
            $releasedLocks = [];

            foreach ($lockIds as $lockId) {
                $lock = Db::name('inventory_lock')->where('id', $lockId)->lock(true)->find();
                
                if (!$lock) {
                    continue; // 记录不存在，跳过
                }

                if ($lock['status'] != self::LOCK_STATUS_LOCKED) {
                    continue; // 状态不是锁定，跳过
                }

                // 更新锁定状态为已释放
                $updated = Db::name('inventory_lock')
                    ->where('id', $lockId)
                    ->update([
                        'status' => self::LOCK_STATUS_RELEASED,
                        'update_time' => time()
                    ]);

                if ($updated) {
                    // 记录释放日志
                    $this->addLockLog([
                        'lock_id' => $lockId,
                        'product_id' => $lock['product_id'],
                        'warehouse_id' => $lock['warehouse_id'],
                        'operation_type' => self::OPERATION_RELEASE,
                        'quantity' => $lock['quantity'],
                        'before_quantity' => $lock['quantity'],
                        'after_quantity' => 0,
                        'ref_type' => $lock['ref_type'],
                        'ref_id' => $lock['ref_id'],
                        'ref_no' => $lock['ref_no'],
                        'notes' => $options['notes'] ?? '释放锁定库存',
                        'created_by' => $options['operation_by'] ?? 0
                    ]);

                    $totalReleasedQuantity += $lock['quantity'];
                    $releasedLocks[] = $lock;
                }
            }

            Db::commit();

            return [
                'code' => 0,
                'msg' => '释放锁定库存成功',
                'data' => [
                    'total_released_quantity' => $totalReleasedQuantity,
                    'released_locks' => $releasedLocks
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 根据业务单据释放锁定
     * @param string $refType 单据类型
     * @param int $refId 单据ID
     * @param array $options 选项参数
     * @return array
     */
    public function releaseByReference($refType, $refId, $options = [])
    {
        $lockIds = Db::name('inventory_lock')
            ->where([
                'ref_type' => $refType,
                'ref_id' => $refId,
                'status' => self::LOCK_STATUS_LOCKED
            ])
            ->column('id');

        if (empty($lockIds)) {
            return [
                'code' => 0,
                'msg' => '没有需要释放的锁定记录',
                'data' => []
            ];
        }

        return $this->releaseLockedInventory($lockIds, $options);
    }

    /**
     * 处理过期锁定
     * @return array
     */
    public function handleExpiredLocks()
    {
        $currentTime = time();
        
        // 查找过期的锁定记录
        $expiredLocks = Db::name('inventory_lock')
            ->where([
                'status' => self::LOCK_STATUS_LOCKED,
                'expire_time' => ['>', 0],
                'expire_time' => ['<', $currentTime],
                'auto_release' => 1
            ])
            ->select();

        if (empty($expiredLocks)) {
            return [
                'code' => 0,
                'msg' => '没有过期的锁定记录',
                'data' => []
            ];
        }

        $expiredCount = 0;
        foreach ($expiredLocks as $lock) {
            try {
                // 更新状态为过期
                Db::name('inventory_lock')
                    ->where('id', $lock['id'])
                    ->update([
                        'status' => self::LOCK_STATUS_EXPIRED,
                        'update_time' => time()
                    ]);

                // 记录过期日志
                $this->addLockLog([
                    'lock_id' => $lock['id'],
                    'product_id' => $lock['product_id'],
                    'warehouse_id' => $lock['warehouse_id'],
                    'operation_type' => self::OPERATION_EXPIRE,
                    'quantity' => $lock['quantity'],
                    'before_quantity' => $lock['quantity'],
                    'after_quantity' => 0,
                    'ref_type' => $lock['ref_type'],
                    'ref_id' => $lock['ref_id'],
                    'ref_no' => $lock['ref_no'],
                    'notes' => '锁定记录已过期自动释放',
                    'created_by' => 0
                ]);

                $expiredCount++;
            } catch (\Exception $e) {
                Log::error('处理过期锁定失败：' . $e->getMessage());
            }
        }

        return [
            'code' => 0,
            'msg' => "处理完成，共处理 {$expiredCount} 条过期锁定记录",
            'data' => ['expired_count' => $expiredCount]
        ];
    }

    /**
     * 查找最佳库存记录进行锁定
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param int $locationId 库位ID
     * @param string $batchNo 批次号
     * @param float $quantity 需要数量
     * @return array|null
     */
    private function findBestInventoryForLock($productId, $warehouseId, $locationId, $batchNo, $quantity)
    {
        $where = ['product_id' => $productId];
        
        if ($warehouseId > 0) {
            $where['warehouse_id'] = $warehouseId;
        }
        
        if ($locationId > 0) {
            $where['location_id'] = $locationId;
        }
        
        if (!empty($batchNo)) {
            $where['batch_no'] = $batchNo;
        }

        // 查找库存充足的记录，优先选择数量最接近需求的
        $inventory = Inventory::where($where)
            ->where('quantity', '>=', $quantity)
            ->order('quantity', 'asc')
            ->find();

        return $inventory ? $inventory->toArray() : null;
    }

    /**
     * 添加锁定日志
     * @param array $logData 日志数据
     */
    private function addLockLog($logData)
    {
        $logData['create_time'] = time();
        Db::name('inventory_lock_log')->insert($logData);
    }

    /**
     * 添加库存变动日志
     * @param array $logData 日志数据
     */
    private function addInventoryLog($logData)
    {
        Db::name('inventory_log')->insert($logData);
    }

    /**
     * 获取产品锁定详情
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @return array
     */
    public function getProductLockDetails($productId, $warehouseId = 0)
    {
        $where = [
            'product_id' => $productId,
            'status' => self::LOCK_STATUS_LOCKED
        ];
        
        if ($warehouseId > 0) {
            $where['warehouse_id'] = $warehouseId;
        }

        return Db::name('inventory_lock')
            ->alias('il')
            ->leftJoin('admin a', 'il.created_by = a.id')
            ->field('il.*, a.name as creator_name')
            ->where($where)
            ->order('il.create_time', 'desc')
            ->select()
            ->toArray();
    }
}