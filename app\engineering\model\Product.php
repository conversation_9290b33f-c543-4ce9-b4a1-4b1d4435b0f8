<?php

namespace app\engineering\model;
use think\Model;
use think\facade\Db;

class Product extends Model
{
    // 在模型中
protected function getCreateTimeAttr($value)
{
    return $value ? date('Y-m-d H:i:s', $value) : '';
}

// 获取器：code字段 (返回material_code的值)
protected function getCodeAttr($value, $data)
{
    return isset($data['material_code']) ? $data['material_code'] : '';
}

// 获取器：name字段 (返回title的值)
protected function getNameAttr($value, $data)
{
    return isset($data['title']) ? $data['title'] : '';
}

// 获取器：spec字段 (返回specs的值)
protected function getSpecAttr($value, $data)
{
    return isset($data['specs']) ? $data['specs'] : '';
}
    /**
     * 获取分页列表
     * @param $where
     * @param $param
     */
    public function datalist($where, $param)
    {
        $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
        $order = empty($param['order']) ? 'p.id desc' : $param['order'];
        try {            
            $list = Db::name('product')
                ->field('p.*, pc.title as cate')
                ->alias('p')
                ->leftJoin('product_cate pc', 'pc.id = p.cate_id')
                ->where($where)
                ->order($order)
                ->paginate(['list_rows' => $rows])
                ->each(function ($item, $key) {
                    $item['admin_name'] = Db::name('Admin')->where('id', $item['admin_id'])->value('name');
                    
                    // 统计产品关联的有效BOM数量
                    $validBomCount = Db::name('bom_master')->where('product_id', $item['id'])
                        ->where('check_status', 2)
                        ->count();
                    $item['has_bom'] = $validBomCount > 0 ? 1 : 0;
                    
                    return $item;
                });
            
            return $list;
        } catch (\Exception $e) {
            // 记录错误日志
            trace('Product datalist error: ' . $e->getMessage(), 'error');
            return [];
        }
    }

    /**
     * 添加数据
     * @param $param
     */
    public function add($param)
    {
        $insertId = 0;
        try {
            $param['create_time'] = time();
            $insertId = self::strict(false)->field(true)->insertGetId($param);
            add_log('add', $insertId, $param);
        } catch(\Exception $e) {
            return to_assign(1, '操作失败，原因：'.$e->getMessage());
        }
        return to_assign(0, '操作成功', ['aid' => $insertId]);
    }

    /**
     * 编辑信息
     * @param $param
     */
    public function edit($param)
    {
        try {
            $param['update_time'] = time();
            self::where('id', $param['id'])->strict(false)->field(true)->update($param);
            add_log('edit', $param['id'], $param);
        } catch(\Exception $e) {
            return to_assign(1, '操作失败，原因：'.$e->getMessage());
        }
        return to_assign();
    }    

    /**
     * 根据id获取信息
     * @param $id
     */
    public function getById($id)
    {
        $info = self::find($id);
        return $info;
    }

    /**
     * 删除信息
     * @param $id
     * @param $type
     * @return array
     */
    public function delById($id, $type = 0)
    {
        try {
            self::where('id', $id)->update(['delete_time' => time()]);
            add_log('delete', $id);
        } catch(\Exception $e) {
            return to_assign(1, '操作失败，原因：'.$e->getMessage());
        }
        return to_assign();
    }
} 