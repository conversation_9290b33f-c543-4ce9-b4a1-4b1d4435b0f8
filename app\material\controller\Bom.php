<?php

namespace app\material\controller;

use app\base\BaseController;
use app\material\model\Bom as BomModel;
use app\material\model\BomItem;
use app\material\model\Material;
use think\facade\Db;
use think\facade\View;

class Bom extends BaseController
{
    /**
     * BOM列表页面
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['bom_code'])) {
                $where[] = ['bom_code', 'like', '%' . $param['bom_code'] . '%'];
            }
            if (!empty($param['product_code'])) {
                $where[] = ['product_code', 'like', '%' . $param['product_code'] . '%'];
            }
            if (!empty($param['product_category'])) {
                $where[] = ['product_category', '=', $param['product_category']];
            }
            if (!empty($param['status'])) {
                $where[] = ['status', '=', $param['status']];
            }
            if (!empty($param['create_user'])) {
                $where[] = ['create_user', 'like', '%' . $param['create_user'] . '%'];
            }

            // 使用原生查询获取BOM列表并关联创建人信息
            $list = Db::name('material_bom')
                ->alias('b')
                ->leftJoin('admin a', 'b.admin_id = a.id')
                ->where($where)
                ->field('b.*,a.name as create_user_name')
                ->order('b.create_time desc')
                ->paginate([
                    'list_rows' => $param['limit'],
                    'page' => $param['page']
                ]);

            // 格式化时间戳为年月日时分秒
            $items = $list->items();
            foreach ($items as &$item) {
                $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_formatted'] = date('Y-m-d H:i:s', $item['update_time']);
                // 设置创建人显示名称
                $item['create_user'] = $item['create_user_name'] ?: '未知';
            }

            // 重新设置格式化后的数据
            $list = $list->toArray();
            $list['data'] = $items;

            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }

    /**
     * 添加/编辑BOM
     */
    public function add()
    {
        $param = get_params();

        if (request()->isPost()) {
            // 验证必填字段
            if (empty($param['bom_code'])) {
                return to_assign(1, 'BOM编号不能为空');
            }
            if (empty($param['bom_name'])) {
                return to_assign(1, 'BOM名称不能为空');
            }
            if (empty($param['product_name'])) {
                return to_assign(1, '产品名称不能为空');
            }
            if (empty($param['product_id'])) {
                return to_assign(1, '产品ID不能为空');
            }

            try {
                // 开启事务
                Db::startTrans();

                // 准备BOM主表数据
                $bomData = [
                    'bom_code' => $param['bom_code'],
                    'bom_name' => $param['bom_name'],
                    'product_id' => $param['product_id'],
                    'product_code' => $param['product_code'] ?? '',
                    'product_name' => $param['product_name'],
                    'customer_id' => $param['customer_id'] ?? 0,
                    'customer_name' => $param['customer_name'] ?? '',
                    'remark' => $param['remark'] ?? '',
                    'admin_id' => $this->uid,
                    'update_time' => time()
                ];

                if (empty($param['id'])) {
                    // 新增
                    // 检查BOM编号是否重复
                    $check = Db::name('material_bom')->where('bom_code', $param['bom_code'])->find();
                    if ($check) {
                        return json(['code' => 1, 'msg' => 'BOM编号已存在!!']);
                    }

                    $bomData['create_time'] = time();
                    $bomId = Db::name('material_bom')->insertGetId($bomData);
                } else {
                    // 编辑
                    $bomId = $param['id'];
                    Db::name('material_bom')->where('id', $bomId)->update($bomData);

                    // 删除原有的BOM明细
                    Db::name('material_bom_detail')->where('bom_id', $bomId)->update(['delete_time' => time()]);
                }

                // 保存BOM明细数据
                if (!empty($param['materials']) && is_array($param['materials'])) {
                    foreach ($param['materials'] as $material) {
                        if (empty($material['material_id'])) {
                            continue;
                        }

                        $detailData = [
                            'bom_id' => $bomId,
                            'bom_code' => $param['bom_code'],
                            'product_id' => $param['product_id'],
                            'material_id' => $material['material_id'],
                            'material_code' => $material['material_code'] ?? '',
                            'material_name' => $material['material_name'] ?? '',
                            'material_category' => $material['material_category'] ?? '',
                            'specifications' => $material['specifications'] ?? '',
                            'material_source' => $material['material_source'] ?? '自购',
                            'bom_level' => $material['bom_level'] ?? 1,
                            'parent_material_id' => $material['parent_material_id'] ?? 0,
                            'quantity' => $material['quantity'] ?? 1,
                            'loss_rate' => $material['loss_rate'] ?? 0,
                            'create_time' => time(),
                            'update_time' => time()
                        ];

                        Db::name('material_bom_detail')->insert($detailData);
                    }
                }

                // 提交事务
                Db::commit();

                 return json(['code' => 0, 'msg' => '保存成功']);


            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return to_assign(1, '保存失败：' . $e->getMessage());
            }
        } else {
            // 处理GET请求 - 显示表单
            $bom = [];
            $materials = [];

            if (!empty($param['id'])) {
                // 获取BOM主表数据
                $bom = Db::name('material_bom')->where('id', $param['id'])->find();
                if (!$bom) {
                    return to_assign(1, 'BOM不存在');
                }

                // 获取BOM明细数据
                $materials = Db::name('material_bom_detail')
                    ->alias('bd')
                    ->leftJoin('product p', 'bd.material_id = p.id')
                    ->leftJoin('product_cate pc', 'p.cate_id = pc.id')
                    ->where('bd.bom_id', $param['id'])
                    ->where('bd.delete_time', 0)
                    ->field('bd.*,p.title as material_name,pc.title as material_category')
                    ->order('bd.bom_level asc, bd.id asc')
                    ->select()
                    ->toArray();
            }

            // 生成BOM编号
            $bom_code = $this->generateBomCode();

            View::assign('bom', $bom);
            View::assign('materials', $materials);
            View::assign('bom_code', $bom_code);
            return view();
        }
    }

    /**
     * 编辑BOM
     */
    public function edit()
    {
        $param = get_params();
        if (request()->isPost()) {
            $id = $param['id'];
            if (empty($id)) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }

            $bom = BomModel::find($id);
            if (!$bom) {
                return json(['code' => 1, 'msg' => 'BOM不存在']);
            }

            try {
                Db::startTrans();
                
                // 更新BOM基础信息（编辑时不允许修改产品信息）
                $bom->bom_name = $param['bom_name'];
                // 编辑时不修改产品相关信息
                // $bom->product_name = $param['product_name'];
                // $bom->product_code = $param['product_code'] ?? '';
                $bom->customer_name = $param['customer_name'] ?? '';
                $bom->customer_code = $param['customer_code'] ?? '';
                $bom->specifications = $param['specifications'] ?? '';
                $bom->attributes = $param['attributes'] ?? '';
                $bom->product_category = $param['product_category'] ?? '';
                $bom->unit_status = $param['unit_status'] ?? '';
                $bom->remark = $param['remark'] ?? '';
                $bom->update_user = session('login_admin.name');
                $bom->update_time = time();
                $bom->save();

                // 物理删除原有物料清单
                $deleteCount = Db::name('material_bom_detail')->where('bom_id', $id)->delete();
                // 记录删除的数量，用于调试
                // error_log("删除了 {$deleteCount} 条BOM明细记录，BOM ID: {$id}");

                // 保存新的物料清单
                if (!empty($param['materials'])) {
                    foreach ($param['materials'] as $material) {
                        $detailData = [
                            'bom_id' => $id,
                            'bom_code' => $bom->bom_code,
                            'product_id' => $bom->product_id,
                            'material_id' => $material['material_id'] ?? 0,
                            'material_code' => $material['material_code'],
                            'material_name' => $material['material_name'],
                            'material_category' => $material['material_category'] ?? '',
                            'specifications' => $material['specifications'] ?? '',
                            'material_source' => $material['material_source'] ?? '自购',
                            'bom_level' => $material['bom_level'] ?? 1,
                            'parent_material_id' => $material['parent_material_id'] ?? 0,
                            'quantity' => $material['quantity'] ?? 1.0000,
                            'unit' => $material['unit'] ?? '',
                            'loss_rate' => $material['loss_rate'] ?? 0.00,
                            'sort' => 0,
                            'remark' => $material['remark'] ?? '',
                            'create_time' => time(),
                            'update_time' => time(),
                            'delete_time' => 0
                        ];

                        Db::name('material_bom_detail')->insert($detailData);
                    }
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return to_assign(1, '更新失败：' . $e->getMessage());
            }
        } else {
            $id = $param['id'];
            $bom = BomModel::find($id);
            if (!$bom) {
                 return json(['code' => 1, 'msg' => 'BOM不存在']);
            }
            
            // 获取BOM物料清单
            $materials = BomItem::where('bom_id', $id)->select();
            
            // 获取产品分类列表
            $productCategories = '';
            
            View::assign('bom', $bom);
            View::assign('materials', $materials);
            View::assign('product_categories', $productCategories);
            return view();
        }
    }

    /**
     * 查看BOM详情
     */
    public function view()
    {
        $param = get_params();
        $id = $param['id'];

        $bom = BomModel::find($id);
        if (!$bom) {
            return to_assign(1, 'BOM不存在');
        }

        // 获取BOM物料清单，关联产品表获取完整信息
        $materials = Db::name('material_bom_detail')
            ->alias('bd')
            ->leftJoin('product p', 'bd.material_id = p.id')
            ->leftJoin('product_cate pc', 'p.cate_id = pc.id')
            ->where('bd.bom_id', $id)
            ->where('bd.delete_time', 0)
            ->field('bd.*,p.material_code,p.title as material_name,p.specs as model,pc.title as material_category')
            ->order('bd.bom_level asc, bd.id asc')
            ->select()
            ->toArray();

        // 格式化BOM时间
        if (!empty($bom['create_time'])) {
            $bom['create_time_text'] = date('Y-m-d H:i:s', $bom['create_time']);
        }
        if (!empty($bom['update_time'])) {
            $bom['update_time_text'] = date('Y-m-d H:i:s', $bom['update_time']);
        }

        View::assign('bom', $bom);
        View::assign('materials', $materials);
        return view();
    }

    /**
     * 删除BOM
     */
    public function delete()
    {
        $param = get_params();
        $id = $param['id'];
        
        if (empty($id)) {
            return to_assign(1, '参数错误');
        }

        try {
            Db::startTrans();
            
            // 删除BOM
            BomModel::destroy($id);
            
            // 删除BOM物料清单
            BomItem::where('bom_id', $id)->delete();
            
            Db::commit();
            return to_assign(0, '删除成功');
        } catch (\Exception $e) {
            Db::rollback();
            return to_assign(1, '删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量删除BOM
     */
    public function batchDelete()
    {
        $param = get_params();
        $ids = $param['ids'];
        
        if (empty($ids)) {
            return to_assign(1, '请选择要删除的数据');
        }

        try {
            Db::startTrans();
            
            // 批量删除BOM
            BomModel::destroy($ids);
            
            // 批量删除BOM物料清单
            BomItem::whereIn('bom_id', $ids)->delete();
            
            Db::commit();
            return to_assign(0, '批量删除成功');
        } catch (\Exception $e) {
            Db::rollback();
            return to_assign(1, '批量删除失败：' . $e->getMessage());
        }
    }

    /**
    /**
     * 获取物料列表（用于选择物料）
     */
    public function getMaterials()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [['p.delete_time', '=', 0], ['p.status', '=', 1]];
            
            // 搜索条件
            if (!empty($param['material_category'])) {
                $where[] = ['p.cate_id', '=', $param['material_category']];
            }
            if (!empty($param['supplier_name'])) {
                $where[] = ['p.producer', 'like', '%' . $param['supplier_name'] . '%'];
            }
            if (!empty($param['material_name'])) {
                $where[] = ['p.title|p.material_code', 'like', '%' . $param['material_name'] . '%'];
            }
            if (!empty($param['model'])) {
                $where[] = ['p.specs', 'like', '%' . $param['model'] . '%'];
            }

            // 排除指定产品（避免将产品本身添加到BOM中）
            if (!empty($param['exclude_product'])) {
                $where[] = ['p.material_code', '<>', $param['exclude_product']];
            }

            $list = Db::name('product')
                ->alias('p')
                ->leftJoin('product_cate pc', 'p.cate_id = pc.id')
                ->where($where)
                ->field('p.id,p.title as name,p.material_code,p.specs as specifications,p.unit,p.brand as model,p.producer,p.cate_id,p.type,p.source_type,p.create_time,p.admin_id,pc.title as category_name')
                ->order('p.create_time desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 10,
                    'page' => $param['page'] ?? 1
                ]);

            // 格式化数据
            $data = $list->toArray();
            foreach ($data['data'] as &$item) {
                $item['category'] = $item['category_name'] ?: '未分类';
                $item['material_name'] = $item['name'];
            }

            return table_assign(0, '', $data);
        } else {
            // 返回选择物料的视图页面
            return view('select_material');
        }
    }

    /**
     * 导出BOM
     */
    public function export()
    {
        $param = get_params();
        $where = [];
        
        // 搜索条件
        if (!empty($param['bom_code'])) {
            $where[] = ['bom_code', 'like', '%' . $param['bom_code'] . '%'];
        }
        if (!empty($param['product_code'])) {
            $where[] = ['product_code', 'like', '%' . $param['product_code'] . '%'];
        }

        $list = BomModel::where($where)->select();
        
        // 这里可以添加导出Excel的逻辑
        return to_assign(0, '导出成功');
    }

    /**
     * 导入BOM
     */
    public function import()
    {
        // 这里可以添加导入Excel的逻辑
        return to_assign(0, '导入成功');
    }

    /**
     * 选择产品页面
     */
    public function selectProduct()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];

            // 搜索条件
            if (!empty($param['keyword'])) {
                $where[] = ['title|material_code', 'like', '%' . $param['keyword'] . '%'];
            }
            if (!empty($param['cate_id'])) {
                $where[] = ['cate_id', '=', $param['cate_id']];
            }

            // 只查询启用的产品
            $where[] = ['status', '=', 1];
            

            $list = Db::name('product')
                ->where($where)
                ->field('id,title,material_code,specs,unit,cate_id')
                ->order('id desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 10,
                    'page' => $param['page'] ?? 1
                ]);

            return table_assign(0, '', $list);
        } else {
            // 获取产品分类
            $categories = Db::name('ProductCate')
                ->where('status', 1)
                ->where('delete_time', 0)
                ->field('id,pid,title,sort')
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            View::assign('categories', $categories);
            return view('selectProduct');
        }
    }

    /**
     * 选择客户页面
     */
    public function selectCustomer()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];

            // 搜索条件
            if (!empty($param['keyword'])) {
                $where[] = ['name', 'like', '%' . $param['keyword'] . '%'];
            }

            // 只查询正常状态的客户（未删除的）
            $where[] = ['discard_time', '=', 0];

            // 先检查表是否存在以及是否有数据
            try {
                $count = Db::name('customer')->count();
                if ($count == 0) {
                    // 如果没有数据，返回空结果
                    return table_assign(0, '', ['data' => [], 'total' => 0, 'per_page' => 10, 'current_page' => 1]);
                }
            } catch (\Exception $e) {
                // 如果表不存在，返回错误
                return table_assign(1, '客户表不存在或查询失败: ' . $e->getMessage(), []);
            }

            $list = Db::name('customer')
                ->alias('c')
                ->leftJoin('admin a', 'c.belong_uid = a.id')
                ->where($where)
                ->field('c.id,c.name,a.name as belong_name,c.address')
                ->order('c.id desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 10,
                    'page' => $param['page'] ?? 1
                ]);

            // 调试：检查查询结果
            $result = $list->toArray();

            return table_assign(0, '', $list);
        } else {
            return view('selectCustomer');
        }
    }

    /**
     * 获取下级BOM
     */
    public function getSubBom()
    {
        $param = get_params();

        if (empty($param['material_id'])) {
            return json(['code' => 1, 'msg' => '物料ID不能为空']);
        }

        try {
            // 查找该物料的BOM记录
            $bomList = Db::name('material_bom_detail')
                ->alias('bd')
                ->leftJoin('product p', 'bd.material_id = p.id')
                ->leftJoin('product_cate pc', 'p.cate_id = pc.id')
                ->where('bd.product_id', $param['material_id'])
                ->where('bd.delete_time', 0)
                ->field('bd.id,bd.material_id,p.material_code,p.title as material_name,bd.material_source,bd.specifications,p.specs as model,pc.title as material_category,bd.quantity,bd.loss_rate')
                ->order('bd.create_time asc')
                ->select();

            $result = [
                'code' => 0,
                'msg' => '获取成功',
                'data' => $bomList->toArray()
            ];

        } catch (\Exception $e) {
            $result = [
                'code' => 1,
                'msg' => '获取失败：' . $e->getMessage(),
                'data' => []
            ];
        }

        return json($result);
    }

    /**
     * 生成BOM编号
     */
    private function generateBomCode()
    {
        $today = date('Ymd');

        // 查找今天的序列记录
        $sequence = Db::name('material_bom_sequence')->where('current_date', $today)->find();

        if (!$sequence) {
            // 如果今天没有记录，创建新记录
            $currentNumber = 1;
            Db::name('material_bom_sequence')->insert([
                'prefix' => 'BOM',
                'date_format' => 'Ymd',
                'current_number' => $currentNumber,
                'current_date' => $today,
                'create_time' => time(),
                'update_time' => time()
            ]);
        } else {
            // 如果有记录，递增序号
            $currentNumber = $sequence['current_number'] + 1;
            Db::name('material_bom_sequence')->where('id', $sequence['id'])->update([
                'current_number' => $currentNumber,
                'update_time' => time()
            ]);
        }

        // 生成BOM编号：BOM + 日期 + 4位序号
        return 'BOM' . $today . str_pad($currentNumber, 4, '0', STR_PAD_LEFT);
    }
}